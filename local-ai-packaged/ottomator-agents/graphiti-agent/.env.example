# Get your Open AI API Key by following these instructions -
# https://help.openai.com/en/articles/4936850-where-do-i-find-my-openai-api-key
OPENAI_API_KEY=

# The LLM to use for the Pydantic AI agent
MODEL_CHOICE=gpt-4.1-mini

# Neo4j (knowledge graph) connection details
# Default values are shown here, you'll likely have to adjust the username and password
NEO4J_URI=bolt://localhost:7687
NEO4J_USER=neo4j
NEO4J_PASSWORD=password