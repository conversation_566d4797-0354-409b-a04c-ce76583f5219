Generate the application description and detailed table definitions for a software application idea about the application subject.

# Output Format

Provide the output in the following JSON format:
```json
{
  "app_description": "[A description of the application based on its name, subject, and type]",
  "tables": [
    {
      "table_name": "[Name of table]",
      "table_description": "[A brief description of what the table stores]",
      "fields": [
        {
          "field_name": "[Name of the field]",
          "field_type": "[Type of the field]",
          "field_length": "[Length of the field]",
          "primary_key": [true/false]
        }
      ],
      "long_description": "[Detailed description of the purpose and relationship of this table with other tables]"
    }
  ]
}
```

# Examples

### For an web application about managing daily tasks:

```json
{
  "app_description": "A web application about managing daily tasks.",
  "tables": [
    {
      "table_name": "tasks",
      "table_description": "Stores details about user-created tasks to manage time and productivity.",
      "fields": [
        {
          "field_name": "task_id",
          "field_type": "integer",
          "field_length": "4",
          "primary_key": true
        },
        {
          "field_name": "task_name",
          "field_type": "varchar",
          "field_length": "255",
          "primary_key": false
        },
        {
          "field_name": "due_date",
          "field_type": "date",
          "field_length": "NA",
          "primary_key": false
        }
      ],
      "long_description": "The 'tasks' table keeps track of all the tasks added by users. Each task has a unique ID, a name, and an optional due date. This table interacts with the 'users' table to link tasks to specific users."
    },
    {
      "table_name": "users",
      "table_description": "Stores basic information about users of the application.",
      "fields": [
        {
          "field_name": "user_id",
          "field_type": "integer",
          "field_length": "4",
          "primary_key": true
        },
        {
          "field_name": "user_name",
          "field_type": "varchar",
          "field_length": "255",
          "primary_key": false
        },
        {
          "field_name": "email",
          "field_type": "varchar",
          "field_length": "255",
          "primary_key": false
        }
      ],
      "long_description": "The 'users' table stores critical information relevant to application users. Each user is identified by a unique user ID, and their tasks can be linked via the 'user_id' field."
    }
  ]
}
```

# Notes

- Include all necessary relationships and constraints that apply to fields (e.g., foreign keys).
- Ensure that the long description adequately explains how each table is used within the context of the application and their relationships.
- The language of the output should be in English.
- Please include specific information focusing on the details of database tables that are part of the application subject. The information should include:
1. Table Name
2. Table Description
3. Field Name
4. Field Type
5. Field Length
6. Primary Key
7. Long description of the relationship between this table and the rest of tables.
