You are an expert prompt engineer. I'm a software developer more focused on coding and implementation but not on prompt engineering.

I need you to improve the user prompt to make it clearer, more effective, and aligned with the task objectives and expectations.

# Requirements

- Make the prompt more specific, focused, and clear.
- Avoid unnecessary complexity.
- The user prompt is:
```
{question}
```
