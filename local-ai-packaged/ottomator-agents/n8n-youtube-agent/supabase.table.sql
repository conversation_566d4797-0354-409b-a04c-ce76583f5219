create table public.videos (
  id bigint generated by default as identity not null,
  created_at timestamp with time zone not null default now(),
  video_id text null,
  title text null,
  description text null,
  summary text null,
  keypoints text[] null,
  actionable text[] null,
  quotes text[] null,
  tags text[] null,
  channel text null,
  transcript text null,
  constraint videos_pkey primary key (id)
) TABLESPACE pg_default;

-- Create a table to store your documents
create table youtube_agent_data (
  id bigserial primary key,
  content text, -- corresponds to Document.pageContent
  metadata jsonb, -- corresponds to Document.metadata
  embedding vector(1536) -- 1536 works for OpenAI embeddings, change if needed
);

-- Create a function to search for documents
create function match_youtube_data (
  query_embedding vector(1536),
  match_count int default null,
  filter jsonb DEFAULT '{}'
) returns table (
  id bigint,
  content text,
  metadata jsonb,
  similarity float
)
language plpgsql
as $$
#variable_conflict use_column
begin
  return query
  select
    id,
    content,
    metadata,
    1 - (youtube_agent_data.embedding <=> query_embedding) as similarity
  from youtube_agent_data
  where metadata @> filter
  order by youtube_agent_data.embedding <=> query_embedding
  limit match_count;
end;
$$;