{"name": "General Research Agent", "nodes": [{"parameters": {"respondWith": "allIncomingItems", "options": {"responseHeaders": {"entries": [{"name": "X-n8n-Signature", "value": ""}]}}}, "id": "b63a9a97-33e9-4768-861e-339cd0354272", "name": "Respond to Webhook", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1.1, "position": [1160, 40]}, {"parameters": {"assignments": {"assignments": [{"id": "ee2bcd57-3b4c-43f9-b4b7-3a25687b9a68", "name": "query", "value": "={{ $json.body.query }}", "type": "string"}, {"id": "63f23e51-af2b-47c4-a288-5abaf9b6c357", "name": "user_id", "value": "={{ $json.body.user_id }}", "type": "string"}, {"id": "b97a3670-8a87-481b-8695-db44624be7d8", "name": "request_id", "value": "={{ $json.body.request_id }}", "type": "string"}, {"id": "7d3fa06d-08f7-4517-b9c5-3c46ff476f55", "name": "session_id", "value": "={{ $json.body.session_id }}", "type": "string"}]}, "options": {}}, "id": "270c1fee-9378-43fb-8dec-87f0ba2c8c1a", "name": "Prep Input Fields", "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [-3880, 320]}, {"parameters": {"assignments": {"assignments": [{"id": "b5eaa2a2-a6bc-40ab-af5e-baa8a5dda1a7", "name": "success", "value": "=true", "type": "boolean"}]}, "options": {}}, "id": "d5a9a050-1276-481c-bf82-222d7f016e77", "name": "Prep Output Fields", "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [820, 40]}, {"parameters": {"httpMethod": "POST", "path": "invoke-general-research-agent", "authentication": "headerAuth", "responseMode": "responseNode", "options": {}}, "id": "45067f96-01dd-4dcc-a421-4e46fb9eab26", "name": "Webhook", "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [-4120, 320], "webhookId": "d9fec84b-86f0-4230-9fd4-c1cb392ff8b5", "credentials": {"httpHeaderAuth": {"id": "o5akNgXQQR74Sezh", "name": "Header Auth account"}}}, {"parameters": {"content": "# Research Agent\n\nAuthor: [<PERSON>](https://www.youtube.com/@54mliu)\n\nThis is a n8n workflow that fetches the articles from Google, reads the articles, determines whether they are relevant to the user's query and then uses relevant articles to generates a brief report\n", "height": 763.4375, "width": 589.875, "color": 6}, "id": "3ef6d5c3-4b5e-45de-a32a-3928fe0b601e", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-5060, 40]}, {"parameters": {"options": {"maxTokens": 16384, "temperature": 0.7}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.1, "position": [-880, 420], "id": "bdf2bad1-edc4-431a-b1f0-8bd30b360a7e", "name": "OpenAI Chat Model", "credentials": {"openAiApi": {"id": "xAeHxzxTT16sMdwS", "name": "Backup OpenAI Account"}}}, {"parameters": {"sessionIdType": "customKey", "sessionKey": "={{$('Prep Input Fields').first().json.session_id}}", "tableName": "messages", "contextWindowLength": 10}, "id": "abb5a2f2-1be3-43c5-a06d-3b1981c75d09", "name": "Postgres Chat Memory", "type": "@n8n/n8n-nodes-langchain.memoryPostgresChat", "typeVersion": 1.3, "position": [440, 260], "credentials": {"postgres": {"id": "PPVUOCGy8dTN3kdl", "name": "Live Agent Studio Test"}}}, {"parameters": {"options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.1, "position": [280, 260], "id": "0303502b-a07b-4e69-840d-cee6cce1b269", "name": "OpenAI Chat Model1", "credentials": {"openAiApi": {"id": "xAeHxzxTT16sMdwS", "name": "Backup OpenAI Account"}}}, {"parameters": {"aggregate": "aggregateAllItemData", "destinationFieldName": "output", "options": {}}, "type": "n8n-nodes-base.aggregate", "typeVersion": 1, "position": [0, 0], "id": "********-55a5-4e6f-8449-8f38d022324d", "name": "Aggregate"}, {"parameters": {"promptType": "define", "text": "=Create a report from these provided summaries", "options": {"systemMessage": "=You are a helpful news reporter tasked with generating a report from search result summaries\n\nOnly use the provided title and summaries in your report and be sure to include links to the original article to read more: {{  $json.toJsonString() }}", "returnIntermediateSteps": false}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.7, "position": [320, 40], "id": "dd252820-1511-4598-bcfb-a39d3830f89b", "name": "Report Agent"}, {"parameters": {"promptType": "define", "text": "=Summarize the HTML page content for each item separately: {{  $json.content }}", "hasOutputParser": true, "options": {"systemMessage": "You are a helpful news reporter bot that reads articles and reports the title and a brief summary of the contents\n\n- Summaries can be no longer than 75 words\n- Ignore any lines that relate to browser incompatibility from web scraping\n"}}, "id": "13caba99-0867-4659-958c-71f4135c90cb", "name": "Summarization Agent", "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.7, "position": [-840, 180], "alwaysOutputData": false, "onError": "continueErrorOutput"}, {"parameters": {"fieldToSplitOut": "output.results", "options": {"destinationFieldName": "url"}}, "type": "n8n-nodes-base.splitOut", "typeVersion": 1, "position": [-3240, 280], "id": "d1c9af0e-2c7c-4dda-84c0-f068ea9b979b", "name": "Split Out2"}, {"parameters": {"assignments": {"assignments": [{"id": "cfe0c00d-bc28-4bb2-bd1f-7138903d5cb5", "name": "url", "value": "={{ $json.url }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [-3020, 280], "id": "33c74e74-9c6f-4f70-b772-c7624a1d7950", "name": "Edit Fields1"}, {"parameters": {"jsonSchemaExample": "{\n\t\"title\": \"California\",\n\t\"summary\": \"Some content\",\n    \"url\": \"https://www.google.com\"\n}"}, "type": "@n8n/n8n-nodes-langchain.outputParserStructured", "typeVersion": 1.2, "position": [-500, 420], "id": "0dd6acf6-7f0a-4d3d-9aea-6825780e614f", "name": "Structured Output Parser1"}, {"parameters": {"sessionIdType": "customKey", "sessionKey": "={{$('Prep Input Fields').item.json.session_id}}"}, "type": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "typeVersion": 1.3, "position": [-760, 560], "id": "a7aa0761-a5bb-4e08-a5ff-f78089a78590", "name": "Window Buffer Memory"}, {"parameters": {"url": "={{ $json.url }}", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "User-Agent", "value": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}, {"name": "Accept", "value": "application/html"}]}, "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [-1920, 260], "id": "8f3e3148-f7d6-43d2-8410-ed784149d71c", "name": "HTTP Request2", "onError": "continueRegularOutput"}, {"parameters": {"toolDescription": "Get 5 search results by providing an input query", "url": "https://api.search.brave.com/res/v1/web/search", "sendQuery": true, "parametersQuery": {"values": [{"name": "q"}, {"name": "count", "valueProvider": "fieldValue", "value": "5"}, {"name": "country", "valueProvider": "fieldValue", "value": "us"}, {"name": "search_lang", "valueProvider": "fieldValue", "value": "en"}, {"name": "spellcheck", "valueProvider": "fieldValue", "value": "1"}]}, "sendHeaders": true, "parametersHeaders": {"values": [{"name": "X-Subscription-Token", "valueProvider": "fieldValue", "value": "Your Brave API Key"}, {"name": "Accept", "valueProvider": "fieldValue", "value": "application/json"}]}}, "type": "@n8n/n8n-nodes-langchain.toolHttpRequest", "typeVersion": 1.1, "position": [-3460, 620], "id": "0784e913-3c93-4939-ac05-f1c1b25b5f52", "name": "Brave Search"}, {"parameters": {"toolDescription": "Get recommended, optimized search queries", "method": "POST", "url": "https://openrouter.ai/api/v1/chat/completions", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "sendBody": true, "specifyBody": "json", "jsonBody": "={\n    \"model\": \"openai/gpt-4o-mini\",\n    \"messages\": [\n      {\n        \"role\": \"user\",\n        \"content\": \"Construct an effective Google search queries to find the most recent and relevant search results that support the input query: {{ $json.query }}. The query should be very concise and your response should not include any unnecessary words\"\n      }\n    ]\n  }"}, "type": "@n8n/n8n-nodes-langchain.toolHttpRequest", "typeVersion": 1.1, "position": [-3600, 700], "id": "9cd2e872-7b10-4294-86bc-cbfa074d9907", "name": "DeepSeek", "credentials": {"httpHeaderAuth": {"id": "kZyfRrAQMmGmZvjD", "name": "Open Router"}}}, {"parameters": {"options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.1, "position": [-3760, 560], "id": "1824e744-67de-4c3b-b07e-d76fae31ee46", "name": "OpenAI Chat Model4", "credentials": {"openAiApi": {"id": "xAeHxzxTT16sMdwS", "name": "Backup OpenAI Account"}}}, {"parameters": {"promptType": "define", "text": "=You are a social media research specialist. Get the URLs for the most recent and relevant search results that support the input query: {{ $json.query }}\n\n- Be sure to collect from various sources\n- You must collect one source from a social media forum like Reddit or Twitter. If you do not have one, generate another search query and append it to your results\n- Determine efficient, optimized search queries to use in your searches\n- Do NOT conduct a search until you have found an optimized query\n- Respond as a json string with the list of URLs under the key \"results\"", "hasOutputParser": true, "options": {}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.7, "position": [-3640, 300], "id": "2d394124-ace6-4726-9837-ac0162028ba8", "name": "Search Optimization Agent"}, {"parameters": {"jsonSchemaExample": "{\n\t\"results\": [\"California\"]\n}"}, "type": "@n8n/n8n-nodes-langchain.outputParserStructured", "typeVersion": 1.2, "position": [-3300, 560], "id": "4d9447e3-a0ab-499a-a642-78691b1c94b6", "name": "Structured Output Parser2"}, {"parameters": {"assignments": {"assignments": [{"id": "4f4a0410-fda8-4f27-895f-94d330801b7e", "name": "title", "value": "={{ $json.output.title }}", "type": "string"}, {"id": "4cb91114-6015-4179-89c7-e7d8d6bb4a21", "name": "summary", "value": "={{ $json.output.summary }}", "type": "string"}, {"id": "c19ab4fe-9fca-4b53-b8b0-a58d15331732", "name": "url", "value": "={{ $json.output.url }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [-240, 140], "id": "4b8b26dc-aa58-42ba-91bc-821bec20cb94", "name": "Edit Fields2"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "42153fee-cf3c-4a81-aed2-0d87d74fd721", "leftValue": "={{ $json.error }}", "rightValue": "", "operator": {"type": "object", "operation": "notExists", "singleValue": true}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.filter", "typeVersion": 2.2, "position": [-1700, 260], "id": "2142b322-e776-48a3-97ce-978e2d10f54f", "name": "Filter1"}, {"parameters": {"operation": "extractHtmlContent", "extractionValues": {"values": [{"key": "content", "cssSelector": "p", "returnArray": true}]}, "options": {}}, "type": "n8n-nodes-base.html", "typeVersion": 1.2, "position": [-1160, 260], "id": "71ee704e-05f9-446d-b359-a6aa8f77dd7b", "name": "HTML"}], "pinData": {"Webhook": [{"json": {"headers": {"host": "halcyoncreations.app.n8n.cloud", "user-agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "content-length": "181", "accept": "*/*", "accept-encoding": "gzip, br", "accept-language": "en-US,en;q=0.9", "authorization": "Bearer hackathon", "cdn-loop": "cloudflare; loops=1; subreqs=1", "cf-connecting-ip": "2600:8800:369f:4900:5507:d75a:621d:3e40", "cf-ew-via": "15", "cf-ipcountry": "US", "cf-ray": "9064f450e16b2ed9-LAX", "cf-visitor": "{\"scheme\":\"https\"}", "cf-worker": "n8n.cloud", "content-type": "application/json", "origin": "https://studio.ottomator.ai", "priority": "u=1, i", "referer": "https://studio.ottomator.ai/", "sec-ch-ua": "\"Google Chrome\";v=\"131\", \"Chromium\";v=\"131\", \"Not_A Brand\";v=\"24\"", "sec-ch-ua-mobile": "?0", "sec-ch-ua-platform": "\"macOS\"", "sec-fetch-dest": "empty", "sec-fetch-mode": "cors", "sec-fetch-site": "cross-site", "x-forwarded-for": "2600:8800:369f:4900:5507:d75a:621d:3e40, **************", "x-forwarded-host": "halcyoncreations.app.n8n.cloud", "x-forwarded-port": "443", "x-forwarded-proto": "https", "x-forwarded-server": "traefik-prod-users-gwc-16-6f778b7769-xsmwk", "x-is-trusted": "yes", "x-real-ip": "2600:8800:369f:4900:5507:d75a:621d:3e40"}, "params": {}, "query": {}, "body": {"query": "ai trends in 2025", "session_id": "621e61f0-3799-4dd7-9554-3c3d796dd0bd", "user_id": "google-oauth2|102689436949142356335", "request_id": "53e04108-d4d6-43ca-a2e9-764d0e49242b"}, "webhookUrl": "https://halcyoncreations.app.n8n.cloud/webhook/d9fec84b-86f0-4230-9fd4-c1cb392ff8b5", "executionMode": "production"}}]}, "connections": {"Prep Output Fields": {"main": [[{"node": "Respond to Webhook", "type": "main", "index": 0}]]}, "Prep Input Fields": {"main": [[{"node": "Search Optimization Agent", "type": "main", "index": 0}]]}, "Webhook": {"main": [[{"node": "Prep Input Fields", "type": "main", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "Summarization Agent", "type": "ai_languageModel", "index": 0}]]}, "Postgres Chat Memory": {"ai_memory": [[{"node": "Report Agent", "type": "ai_memory", "index": 0}]]}, "OpenAI Chat Model1": {"ai_languageModel": [[{"node": "Report Agent", "type": "ai_languageModel", "index": 0}]]}, "Aggregate": {"main": [[{"node": "Report Agent", "type": "main", "index": 0}]]}, "Report Agent": {"main": [[{"node": "Prep Output Fields", "type": "main", "index": 0}]]}, "Summarization Agent": {"main": [[{"node": "Edit Fields2", "type": "main", "index": 0}]]}, "Split Out2": {"main": [[{"node": "Edit Fields1", "type": "main", "index": 0}]]}, "Edit Fields1": {"main": [[{"node": "HTTP Request2", "type": "main", "index": 0}]]}, "Structured Output Parser1": {"ai_outputParser": [[{"node": "Summarization Agent", "type": "ai_outputParser", "index": 0}]]}, "Window Buffer Memory": {"ai_memory": [[{"node": "Summarization Agent", "type": "ai_memory", "index": 0}]]}, "HTTP Request2": {"main": [[{"node": "Filter1", "type": "main", "index": 0}]]}, "Brave Search": {"ai_tool": [[{"node": "Search Optimization Agent", "type": "ai_tool", "index": 0}]]}, "DeepSeek": {"ai_tool": [[{"node": "Search Optimization Agent", "type": "ai_tool", "index": 0}]]}, "OpenAI Chat Model4": {"ai_languageModel": [[{"node": "Search Optimization Agent", "type": "ai_languageModel", "index": 0}]]}, "Search Optimization Agent": {"main": [[{"node": "Split Out2", "type": "main", "index": 0}]]}, "Structured Output Parser2": {"ai_outputParser": [[{"node": "Search Optimization Agent", "type": "ai_outputParser", "index": 0}]]}, "Edit Fields2": {"main": [[{"node": "Aggregate", "type": "main", "index": 0}]]}, "Filter1": {"main": [[{"node": "HTML", "type": "main", "index": 0}]]}, "HTML": {"main": [[{"node": "Summarization Agent", "type": "main", "index": 0}]]}}, "active": true, "settings": {"executionOrder": "v1"}, "versionId": "7d8ebd5a-7257-47a9-86f1-a03bb48ab5e2", "meta": {"templateCredsSetupCompleted": true, "instanceId": "f65a08c0adc90a3cde2c633d24c6daecde3817033b75588ee10a781b0b7aa3f5"}, "id": "VvFfvErLSbZsVnFy", "tags": [{"createdAt": "2024-12-10T13:21:06.912Z", "updatedAt": "2024-12-10T13:21:06.912Z", "id": "0tXJXfH2daB7QdK5", "name": "studio-test"}]}