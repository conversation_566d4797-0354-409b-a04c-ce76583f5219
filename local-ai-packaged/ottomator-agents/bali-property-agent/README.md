# Propertia Bali Chat Agent

Author: [<PERSON><PERSON><PERSON><PERSON>](https://www.youtube.com/watch?v=P8I8bfrpn_w)

**Platform:** Voiceflow (you can import the .vf file into your own Voiceflow to check out the flow)

**Note:** All API keys have been removed from the .vf file

A smart conversational agent designed for Propertia Bali, a real estate agency. It captures and qualifies leads at the top of the funnel, analyzes user queries step by step, and assists buyers in exploring properties. With advanced workflows, it focuses on providing personalized recommendations, scheduling property visits, and answering FAQs, allowing realtors to handle only the most crucial tasks.

See the .txt file in this directory for the knowledge base used in Voiceflow.

## Features

- Recommends matching properties based on buyer preferences  
- Helps buyers explore nearby amenities for their chosen property  
- Integrates Google Maps details (distance & duration)  
- Manages property visit bookings, syncing with realtor’s calendar and availability  
- Handles FAQs using a comprehensive knowledge base  
- Updates CRM with lead details and preferences for marketing campaigns  

## How It Works

1. Analyzes user queries and filters potential leads  
2. Matches user preferences with available listings  
3. Offers real-time map insights through Google Maps  
4. Schedules property tours based on calendar availability  
5. Answers FAQs about the agency and its offerings  
6. Logs qualified leads and preferences into the CRM  

## Contributing

This agent is part of the oTTomator agents collection. For contributions or issues, please refer to the main repository guidelines.


