{"name": "Advanced Web Researcher", "nodes": [{"parameters": {"assignments": {"assignments": [{"id": "ee2bcd57-3b4c-43f9-b4b7-3a25687b9a68", "name": "query", "value": "={{ $json.body.query }}", "type": "string"}, {"id": "63f23e51-af2b-47c4-a288-5abaf9b6c357", "name": "user_id", "value": "={{ $json.body.user_id }}", "type": "string"}, {"id": "b97a3670-8a87-481b-8695-db44624be7d8", "name": "request_id", "value": "={{ $json.body.request_id }}", "type": "string"}, {"id": "b4a4acb2-b2c4-45c1-af2e-02636999af94", "name": "session_id", "value": "={{ $json.body.session_id }}", "type": "string"}]}, "options": {}}, "id": "3e4c61bc-2191-40d2-9c34-1e359bc70f5d", "name": "<PERSON>", "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [1380, -60]}, {"parameters": {"respondWith": "allIncomingItems", "options": {"responseHeaders": {"entries": [{"name": "X-n8n-Signature", "value": "EvtIS^EBVISeie6svB@6ev"}]}}}, "id": "acd98281-00a2-4413-9ae8-4515be12dae5", "name": "Respond to Webhook", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1.1, "position": [2020, -60]}, {"parameters": {"sessionIdType": "customKey", "sessionKey": "={{ $('Edit Fields').item.json.session_id }}", "tableName": "messages"}, "id": "870d7da9-6133-4a3e-ad96-49076fafa581", "name": "Postgres Chat Memory", "type": "@n8n/n8n-nodes-langchain.memoryPostgresChat", "typeVersion": 1, "position": [1680, 160], "credentials": {"postgres": {"id": "erIa9T64hNNeDuvB", "name": "Prod Postgres account"}}}, {"parameters": {"options": {}}, "id": "bc8e3ed1-d28c-49e5-84bd-6dfbf22de4c8", "name": "OpenAI Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1, "position": [1540, 160], "credentials": {"openAiApi": {"id": "05Q6PbnSdyEcu9Ze", "name": "OpenAi account"}}}, {"parameters": {"name": "web_search", "description": "Call this tool to search the web given a query that you come up with to help answer the user's question.", "workflowId": {"__rl": true, "value": "={{ $workflow.id }}", "mode": "id"}, "responsePropertyName": "output", "fields": {"values": [{"name": "session_id", "stringValue": "={{$('Edit Fields').item.json.session_id}}"}]}, "specifyInputSchema": true, "jsonSchemaExample": "{\n\t\"query\": \"React+19+Updates\"\n}"}, "id": "eba4be88-a2af-423d-8bc8-a96c04e51ad5", "name": "Call n8n Workflow Tool", "type": "@n8n/n8n-nodes-langchain.toolWorkflow", "typeVersion": 1.2, "position": [1820, 160]}, {"parameters": {}, "id": "8dda872b-e5d4-41d6-964c-45d066907c19", "name": "Execute Workflow Trigger", "type": "n8n-nodes-base.executeWorkflowTrigger", "typeVersion": 1, "position": [1060, 400]}, {"parameters": {"tableId": "messages", "fieldsUi": {"fieldValues": [{"fieldId": "session_id", "fieldValue": "={{ $('Execute Workflow Trigger').item.json.session_id }}"}, {"fieldId": "message", "fieldValue": "={{ {\n\"type\": \"ai\",\n\"content\": `-> Searching the web with query: ${$('Execute Workflow Trigger').item.json.query.query.replaceAll(\"+\", \" \")}`,\n\"data\": {},\n\"additional_kwargs\": {},\n\"response_metadata\": {}\n} }}"}]}}, "id": "7db86219-e0ff-4257-bf33-639eb4c5a967", "name": "Add AI Message to DB", "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [1300, 400], "credentials": {"supabaseApi": {"id": "hOLIm3Jeg9JcG616", "name": "Prod Supabase account"}}}, {"parameters": {"url": "=https://api.search.brave.com/res/v1/web/search?q={{ $('Execute Workflow Trigger').item.json.query.query }} }}&summary=1", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Accept", "value": "application/json"}, {"name": "Accept-Encoding", "value": "gzip"}]}, "options": {}}, "id": "13720dbf-adf3-4495-9a81-b39f2b078bba", "name": "HTTP Request", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1540, 400], "credentials": {"httpHeaderAuth": {"id": "vQywoUkt33Kl6bxG", "name": "Brave API"}}}, {"parameters": {"tableId": "messages", "fieldsUi": {"fieldValues": [{"fieldId": "session_id", "fieldValue": "={{ $('Execute Workflow Trigger').item.json.session_id }}"}, {"fieldId": "message", "fieldValue": "={{ {\n\"type\": \"ai\",\n\"content\": \"-> Summarizing web research...\",\n\"data\": {},\n\"additional_kwargs\": {},\n\"response_metadata\": {}\n} }}"}]}}, "id": "e3859c7d-e165-4bd3-8d63-28df48f7df3c", "name": "Add AI Message to DB1", "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [1800, 400], "credentials": {"supabaseApi": {"id": "hOLIm3Jeg9JcG616", "name": "Prod Supabase account"}}}, {"parameters": {"url": "=https://api.search.brave.com/res/v1/summarizer/search?key={{ $('HTTP Request').item.json.summarizer.key }}&entity_info=1", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Accept", "value": "application/json"}, {"name": "Accept-Encoding", "value": "gzip"}]}, "options": {}}, "id": "f23367b2-61ef-4250-bd0f-6465ad4a8f50", "name": "HTTP Request1", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [2040, 400], "credentials": {"httpHeaderAuth": {"id": "vQywoUkt33Kl6bxG", "name": "Brave API"}}}, {"parameters": {"assignments": {"assignments": [{"id": "794f24a8-8730-44c6-992a-d3d5bf1ae1c5", "name": "output", "value": "={{ $('HTTP Request1').item.json.summary.toJsonString() }}", "type": "string"}]}, "options": {}}, "id": "e381c064-b5a6-46ac-a303-ad90d6bd44b7", "name": "Edit Fields1", "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [2260, 400]}, {"parameters": {"httpMethod": "POST", "path": "invoke-advanced-web-researcher", "authentication": "headerAuth", "responseMode": "responseNode", "options": {}}, "id": "39174a3c-ca54-4ee1-9ca6-b9c98f78121e", "name": "Webhook", "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [1140, -60], "webhookId": "0f92c60b-95c0-448c-a504-1b3e57ef0b88", "credentials": {"httpHeaderAuth": {"id": "o5akNgXQQR74Sezh", "name": "Header Auth account"}}}, {"parameters": {"promptType": "define", "text": "={{ $('Edit Fields').item.json.query }}", "options": {"systemMessage": "=You are an expert at researching the web to answer user questions that you can't answer yourself. You have access to the Brave API through a tool to do so. You give it a query and then it gives you back a summary of the articles found in from searching the web with the query you gave.\n\nNote: All queries you pass into the tool for web research most not have spaces. Instead you need to use plus signs. This is because the query will be used with the \"q\" parameter in a GET request.", "maxIterations": 5}}, "id": "02f411a2-31f2-43ee-bb7e-10050018aaa7", "name": "AI Agent", "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.6, "position": [1620, -60]}, {"parameters": {"content": "# Advanced Web Researcher Agent\n\nAuthor: [<PERSON>](https://www.youtube.com/@ColeMedin)\n\nThis n8n-powered agent is an advanced web research assistant that leverages the Brave Search API to perform comprehensive online research. Unlike traditional web research tools, it uses Brave's powerful search capabilities combined with AI summarization to provide more accurate, detailed, and relevant information from across the web.\n\n## Features\n\n- Utilizes Brave Search API for high-quality search results\n- Automatically summarizes articles and web content\n- Provides comprehensive research summaries\n- Maintains source attribution\n- Filters and prioritizes relevant information\n- Handles complex research queries", "height": 441.41627906976817, "width": 651.0139534883727, "color": 6}, "id": "ef855151-1566-47be-834f-8aa86128db4f", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [380, -100]}], "pinData": {"Webhook": [{"json": {"headers": {"host": "n8n.chayahfitness.com", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "content-length": "192", "accept": "*/*", "accept-encoding": "deflate, gzip", "authorization": "Bearer 7vnvV*ELn.#FWNV(87g2WQc", "content-type": "application/json", "x-forwarded-for": "*************", "x-forwarded-host": "n8n.chayahfitness.com", "x-forwarded-proto": "https", "x-real-ip": "2601:441:4380:40b0:b4b3:724b:27e1:c4ba"}, "params": {}, "query": {}, "body": {"query": "What are the latest updates to React with React 19?", "user_id": "google-oauth2|116467443974012389959", "request_id": "f98asdyf987yasd0f987asdf8", "session_id": "google-oauth2|116467443974012389959~2~8dfbddbe603d"}, "webhookUrl": "https://n8n.chayahfitness.com/webhook-test/invoke-n8n-expert", "executionMode": "test"}}]}, "connections": {"Edit Fields": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}]]}, "Postgres Chat Memory": {"ai_memory": [[{"node": "AI Agent", "type": "ai_memory", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}]]}, "Call n8n Workflow Tool": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "Add AI Message to DB": {"main": [[{"node": "HTTP Request", "type": "main", "index": 0}]]}, "HTTP Request": {"main": [[{"node": "Add AI Message to DB1", "type": "main", "index": 0}]]}, "Add AI Message to DB1": {"main": [[{"node": "HTTP Request1", "type": "main", "index": 0}]]}, "HTTP Request1": {"main": [[{"node": "Edit Fields1", "type": "main", "index": 0}]]}, "Webhook": {"main": [[{"node": "<PERSON>", "type": "main", "index": 0}]]}, "Execute Workflow Trigger": {"main": [[{"node": "Add AI Message to DB", "type": "main", "index": 0}]]}, "AI Agent": {"main": [[{"node": "Respond to Webhook", "type": "main", "index": 0}]]}}, "active": true, "settings": {"executionOrder": "v1"}, "versionId": "d1380028-99fc-4ff1-8fcd-502f7e726c1b", "meta": {"templateCredsSetupCompleted": true, "instanceId": "f65a08c0adc90a3cde2c633d24c6daecde3817033b75588ee10a781b0b7aa3f5"}, "id": "rfWRQ9Uxaj2no7v1", "tags": [{"createdAt": "2024-12-09T14:35:20.507Z", "updatedAt": "2024-12-09T14:35:20.507Z", "id": "wBUwbX8AS8QmBHOC", "name": "studio-prod"}]}