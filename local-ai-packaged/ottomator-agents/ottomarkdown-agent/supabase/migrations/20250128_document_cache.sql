-- Create document cache table
create table if not exists document_cache (
    id bigint generated by default as identity primary key,
    doc_hash text not null unique,
    file_name text not null,
    file_type text not null,
    markdown_content text not null,
    created_at timestamp with time zone not null,
    last_accessed timestamp with time zone not null
);

-- Create index for faster lookups
create index if not exists idx_document_cache_hash on document_cache(doc_hash);

-- Add RLS policies
alter table document_cache enable row level security;

-- Allow read access to authenticated users
create policy "Users can read document cache"
    on document_cache for select
    using (true);

-- Allow insert/update access to authenticated users
create policy "Users can insert document cache"
    on document_cache for insert
    with check (true);

create policy "Users can update document cache"
    on document_cache for update
    using (true);
