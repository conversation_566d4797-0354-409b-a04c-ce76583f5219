-- Drop existing table and related objects
drop table if exists messages cascade;

-- Create messages table
create table messages (
    id bigint generated by default as identity primary key,
    session_id text not null,
    message_type text not null,
    content text not null,
    data jsonb,
    created_at timestamp with time zone not null default now()
);

-- Create index for faster lookups
create index idx_messages_session on messages(session_id);

-- Add RLS policies
alter table messages enable row level security;

-- Allow read access to authenticated users
create policy "Users can read messages"
    on messages for select
    using (true);

-- Allow insert access to authenticated users
create policy "Users can insert messages"
    on messages for insert
    with check (true);

-- Allow update access to authenticated users
create policy "Users can update messages"
    on messages for update
    using (true);

-- Grant necessary permissions
grant all privileges on table messages to authenticated;
grant all privileges on sequence messages_id_seq to authenticated;

-- Notify Supabase of schema changes
notify pgrst, 'reload schema';
