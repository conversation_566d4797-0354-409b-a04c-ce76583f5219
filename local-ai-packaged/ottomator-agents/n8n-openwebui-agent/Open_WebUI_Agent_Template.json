{"name": "Open WebUI Agent Template", "nodes": [{"parameters": {"options": {}}, "id": "405066b4-56f0-4a51-939c-a94c8067f8ef", "name": "OpenAI Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1, "position": [-640, 560], "credentials": {"openAiApi": {"id": "05Q6PbnSdyEcu9Ze", "name": "OpenAi account"}}}, {"parameters": {"sessionIdType": "customKey", "sessionKey": "={{ $json.body.sessionId }}"}, "id": "573a013f-9f44-4356-82c1-02034a48d42f", "name": "Postgres Chat Memory", "type": "@n8n/n8n-nodes-langchain.memoryPostgresChat", "typeVersion": 1, "position": [-460, 560], "notesInFlow": false, "credentials": {"postgres": {"id": "PPVUOCGy8dTN3kdl", "name": "Live Agent Studio Test"}}}, {"parameters": {"content": "## AI Agent with Webhook for Open WebUI", "height": 525, "width": 1596, "color": 6}, "id": "f63e9ae3-6a93-44de-a202-90fd9349d84b", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-1560, 220]}, {"parameters": {"options": {}}, "id": "4136a9a8-d005-4d87-9c0a-25d1ec0a9ffb", "name": "Respond to Webhook", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1.1, "position": [-200, 300]}, {"parameters": {"httpMethod": "POST", "path": "invoke-n8n-agent", "authentication": "headerAuth", "responseMode": "responseNode", "options": {}}, "id": "e78b6ddb-4734-4911-b291-5ddedaf05016", "name": "Webhook", "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [-1500, 380], "webhookId": "7f8a71dd-b98a-4c87-aa3c-c0f7c3b63535", "credentials": {"httpHeaderAuth": {"id": "o5akNgXQQR74Sezh", "name": "Header Auth account"}}}, {"parameters": {"name": "web_search", "description": "Call this tool to do an advanced web search based on a query you define.", "workflowId": {"__rl": true, "value": "={{ $workflow.id }}", "mode": "id"}, "workflowInputs": {"mappingMode": "defineBelow", "value": {"query": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('query', ``, 'string') }}"}, "matchingColumns": [], "schema": [{"id": "tool_type", "displayName": "tool_type", "required": false, "defaultMatch": false, "display": true, "canBeUsedToMatch": true, "type": "string", "removed": true}, {"id": "query", "displayName": "query", "required": false, "defaultMatch": false, "display": true, "canBeUsedToMatch": true, "type": "string"}, {"id": "image_url", "displayName": "image_url", "required": false, "defaultMatch": false, "display": true, "canBeUsedToMatch": true, "type": "string", "removed": true}], "attemptToConvertTypes": false, "convertFieldsToString": false}}, "type": "@n8n/n8n-nodes-langchain.toolWorkflow", "typeVersion": 2, "position": [-300, 560], "id": "41a56129-7449-4efb-9695-930e52f7b640", "name": "Web Search Tool"}, {"parameters": {"content": "## Example Agent Tool", "height": 340, "width": 680, "color": 4}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-640, 760], "id": "441ac286-7313-4c1f-b222-fc2fed3ec8a0", "name": "Sticky Note7"}, {"parameters": {"url": "=https://api.search.brave.com/res/v1/web/search?q={{ $('Tool Start').item.json.query }} }}&summary=1", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Accept", "value": "application/json"}, {"name": "Accept-Encoding", "value": "gzip"}]}, "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [-360, 860], "id": "e423a192-c546-42cd-9565-5f7982c7aa00", "name": "Brave Web Search", "credentials": {"httpHeaderAuth": {"id": "vQywoUkt33Kl6bxG", "name": "Brave API"}}}, {"parameters": {"workflowInputs": {"values": [{"name": "query"}]}}, "type": "n8n-nodes-base.executeWorkflowTrigger", "typeVersion": 1.1, "position": [-580, 860], "id": "4c89122b-b406-4141-bc3c-7e63dafd0968", "name": "Tool Start"}, {"parameters": {"url": "=https://api.search.brave.com/res/v1/summarizer/search?key={{ $json.summarizer.key }}&entity_info=1", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Accept", "value": "application/json"}, {"name": "Accept-Encoding", "value": "gzip"}]}, "options": {}}, "id": "067241db-a694-4709-a1f7-2d0a7e11223b", "name": "Summarize Web Research", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [-160, 860], "credentials": {"httpHeaderAuth": {"id": "vQywoUkt33Kl6bxG", "name": "Brave API"}}}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "f5ebbd4f-6549-4a31-b3f8-eee7634dc439", "leftValue": "={{ $json.body.sessionId }}", "rightValue": "None", "operator": {"type": "string", "operation": "notEquals"}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [-1300, 380], "id": "d20be913-7202-4155-8c48-a24384ff4d4c", "name": "If"}, {"parameters": {"model": {"__rl": true, "mode": "list", "value": "gpt-4o-mini"}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [-1000, 620], "id": "fec49db7-7093-4f76-93a5-639412970ae5", "name": "OpenAI Chat Model1", "credentials": {"openAiApi": {"id": "05Q6PbnSdyEcu9Ze", "name": "OpenAi account"}}}, {"parameters": {"assignments": {"assignments": [{"id": "d264444f-c01a-4fa0-86a4-c0bf0e4c8537", "name": "output", "value": "={{ $json.output || $json.text }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [-420, 300], "id": "de256928-39d5-4bff-9c89-2b35310b8e75", "name": "Edit Fields (Set Output Field)"}, {"parameters": {"promptType": "define", "text": "={{ $json.body.chatInput }}", "options": {"systemMessage": "You are a personal assistant who helps answer questions from a corpus of documents. The documents are either text based (Txt, docs, extracted PDFs, etc.) or tabular data (CSVs or Excel documents).\n\nYou are given tools to perform RAG in the 'documents' table, look up the documents available in your knowledge base in the 'document_metadata' table, extract all the text from a given document, and query the tabular files with SQL in the 'document_rows' table.\n\nAlways start by performing RAG unless the question requires a SQL query for tabular data (fetching a sum, finding a max, something a RAG lookup would be unreliable for). If RAG doesn't help, then look at the documents that are available to you, find a few that you think would contain the answer, and then analyze those.\n\nAlways tell the user if you didn't find the answer. Don't make something up just to please them."}}, "id": "307aa3e6-8e90-4f61-89cc-c736a5651c8f", "name": "Primary AI Agent", "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.6, "position": [-900, 300]}, {"parameters": {"promptType": "define", "text": "={{ $('Webhook').item.json.body.chatInput }}"}, "type": "@n8n/n8n-nodes-langchain.chainLlm", "typeVersion": 1.5, "position": [-1100, 480], "id": "77de21fb-fe2e-4796-97f3-26ce2cdfdefb", "name": "Open WebUI Metadata LLM"}], "pinData": {}, "connections": {"OpenAI Chat Model": {"ai_languageModel": [[{"node": "Primary AI Agent", "type": "ai_languageModel", "index": 0}]]}, "Postgres Chat Memory": {"ai_memory": [[{"node": "Primary AI Agent", "type": "ai_memory", "index": 0}]]}, "Webhook": {"main": [[{"node": "If", "type": "main", "index": 0}]]}, "Respond to Webhook": {"main": [[]]}, "Web Search Tool": {"ai_tool": [[{"node": "Primary AI Agent", "type": "ai_tool", "index": 0}]]}, "Tool Start": {"main": [[{"node": "Brave Web Search", "type": "main", "index": 0}]]}, "Brave Web Search": {"main": [[{"node": "Summarize Web Research", "type": "main", "index": 0}]]}, "If": {"main": [[{"node": "Primary AI Agent", "type": "main", "index": 0}], [{"node": "Open WebUI Metadata LLM", "type": "main", "index": 0}]]}, "OpenAI Chat Model1": {"ai_languageModel": [[{"node": "Open WebUI Metadata LLM", "type": "ai_languageModel", "index": 0}]]}, "Edit Fields (Set Output Field)": {"main": [[{"node": "Respond to Webhook", "type": "main", "index": 0}]]}, "Primary AI Agent": {"main": [[{"node": "Edit Fields (Set Output Field)", "type": "main", "index": 0}]]}, "Open WebUI Metadata LLM": {"main": [[{"node": "Edit Fields (Set Output Field)", "type": "main", "index": 0}]]}}, "active": true, "settings": {"executionOrder": "v1"}, "versionId": "6d5ce905-6d74-4f88-869f-b554894f7c81", "meta": {"templateCredsSetupCompleted": true, "instanceId": "f65a08c0adc90a3cde2c633d24c6daecde3817033b75588ee10a781b0b7aa3f5"}, "id": "D30OKjXg1D8MlT4p", "tags": []}