# AI Assistant for Enish Restaurant

Author: [Genius Excel](https://web.dojo.app/create_booking/vendor/I8Ks7WgYmNPf0ycpHQCGc51J-UFCISUQfEQocE4m6GQ_restaurant)

**Platform:** Voiceflow (you can import the .vf file into your own Voiceflow to check out the flow)

**Note:** All API keys have been removed from the .vf file

Revolutionizing table reservations with intelligence and convenience, this Voiceflow-built AI assistant delivers a seamless, user-friendly experience. By guiding customers through the reservation process, calculating deposit fees, and sending real-time confirmations, it elevates dining experiences while streamlining restaurant operations.

## Features

1. **Intelligent Table Reservations**: Dynamically adjusts the reservation flow based on party size.  
2. **Dynamic Deposit Fee Calculation**: Automatically calculates fees per party size for clarity and efficiency.  
3. **Seamless Payment Integration**: Powered by Stripe, ensuring secure deposits and real-time payment confirmation.  
4. **Instant Confirmation**: Sends automated email confirmations immediately after successful payments.  
5. **Smart Payment Recovery**: Handles payment failures gracefully by guiding users to retry or exit.  
6. **Hassle-Free Cancellations & Refunds**: Uses OTP verification for secure cancellations and processes refunds automatically.  
7. **Timely Email Notifications**: Keeps users informed with automated reservation and cancellation updates.

## How It Works

1. Guides customers through each reservation step, adapting to party size.  
2. Calculates deposit fees automatically and collects secure payments via Stripe.  
3. Triggers real-time confirmations and email updates for both bookings and cancellations.  
4. Handles payment failures and refunds seamlessly, keeping customers informed at every step.  
5. Uses OTP verification for secure cancellations, providing a reliable and user-friendly experience.

## Contributing

This agent is part of the oTTomator agents collection. For contributions or issues, please refer to the main repository guidelines.


