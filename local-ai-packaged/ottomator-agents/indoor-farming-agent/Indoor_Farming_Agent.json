{"name": "Indoor Farming Agent", "nodes": [{"parameters": {"options": {}}, "id": "f9ffdee9-8b38-4bc2-a43f-e185a8341196", "name": "OpenAI Chat Model1", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1, "position": [2180, 460], "credentials": {"openAiApi": {"id": "xAeHxzxTT16sMdwS", "name": "Backup OpenAI Account"}}}, {"parameters": {"jsonMode": "expressionData", "jsonData": "={{ $json.data || $json.text || $json.concatenated_data }}", "options": {"metadata": {"metadataValues": [{"name": "=file_id", "value": "={{ $('Download Text').item.json.metadata.file_id }}"}]}}}, "id": "a3c7e1f6-aa1d-4714-b2c5-d75f24667bda", "name": "Default Data Loader", "type": "@n8n/n8n-nodes-langchain.documentDefaultDataLoader", "typeVersion": 1, "position": [2140, 1240]}, {"parameters": {"model": "text-embedding-3-small", "options": {}}, "id": "e37e744c-bb04-46e1-a128-4d13e083d698", "name": "Embeddings OpenAI1", "type": "@n8n/n8n-nodes-langchain.embeddingsOpenAi", "typeVersion": 1, "position": [1920, 1300], "credentials": {"openAiApi": {"id": "xAeHxzxTT16sMdwS", "name": "Backup OpenAI Account"}}}, {"parameters": {"name": "user_documents", "description": "Contains all the user's documents that you can check for context to answer user questions."}, "id": "9d3559b7-c9de-4c2d-bc19-d2ca521c0338", "name": "Retrieve Documents", "type": "@n8n/n8n-nodes-langchain.toolVectorStore", "typeVersion": 1, "position": [1800, 360]}, {"parameters": {"model": "text-embedding-3-small", "options": {}}, "id": "c7d54698-0f44-4dde-92aa-7e29089d311d", "name": "Embeddings OpenAI", "type": "@n8n/n8n-nodes-langchain.embeddingsOpenAi", "typeVersion": 1, "position": [1980, 760], "credentials": {"openAiApi": {"id": "xAeHxzxTT16sMdwS", "name": "Backup OpenAI Account"}}}, {"parameters": {"sessionIdType": "customKey", "sessionKey": "={{ $('Prep Input Fields').item.json.session_id }}"}, "id": "be640347-8482-4b77-b402-502db940e1ba", "name": "Postgres Chat Memory", "type": "@n8n/n8n-nodes-langchain.memoryPostgresChat", "typeVersion": 1, "position": [960, 580], "notesInFlow": false, "credentials": {"postgres": {"id": "erIa9T64hNNeDuvB", "name": "Prod Postgres account"}}}, {"parameters": {"operation": "delete", "tableId": "sensor_data", "filterType": "string", "filterString": "=metadata->>file_id=like.*{{ $json.file_id }}*"}, "id": "2a601eb3-3c62-403a-86af-5abe5f926b89", "name": "Delete Old Doc Rows", "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [920, 1000], "alwaysOutputData": true, "credentials": {"supabaseApi": {"id": "hOLIm3Jeg9JcG616", "name": "Prod Supabase account"}}}, {"parameters": {"respondWith": "allIncomingItems", "options": {"responseHeaders": {"entries": [{"name": "X-n8n-Signature", "value": "EvtIS^EBVISeie6svB@6ev"}]}}}, "id": "669c8a5a-1adb-4fd2-acb1-bf57752dbc3e", "name": "Respond to Webhook", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1.1, "position": [2220, 120]}, {"parameters": {"public": true, "options": {}}, "id": "ec94b853-13aa-46cb-98a3-16143137911b", "name": "When chat message received", "type": "@n8n/n8n-nodes-langchain.chatTrigger", "typeVersion": 1.1, "position": [0, 0], "webhookId": "15e4d662-3f98-48d0-9f50-68838769ecac"}, {"parameters": {"httpMethod": "POST", "path": "invoke-farming-agent", "authentication": "headerAuth", "responseMode": "responseNode", "options": {}}, "id": "748f1c4f-013a-4e8f-861b-0c3daf754363", "name": "Webhook", "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [-500, 580], "webhookId": "9ba11544-5c4e-4f91-818a-08a4ecb596c5", "credentials": {"httpHeaderAuth": {"id": "o5akNgXQQR74Sezh", "name": "Header Auth account"}}}, {"parameters": {}, "id": "********-fe7a-4e9f-b3bb-dbcaeb6be1b8", "name": "Character Text Splitter", "type": "@n8n/n8n-nodes-langchain.textSplitterCharacterTextSplitter", "typeVersion": 1, "position": [2400, 1480]}, {"parameters": {"mode": "insert", "tableName": {"__rl": true, "value": "sensor_data", "mode": "list", "cachedResultName": "sensor_data"}, "options": {}}, "id": "6dcb5536-89f1-4af0-acea-32434f3c54c0", "name": "Insert into Supabase Vectorstore", "type": "@n8n/n8n-nodes-langchain.vectorStoreSupabase", "typeVersion": 1, "position": [1720, 1000], "credentials": {"supabaseApi": {"id": "hOLIm3Jeg9JcG616", "name": "Prod Supabase account"}}}, {"parameters": {"tableName": {"__rl": true, "value": "sensor_data", "mode": "list", "cachedResultName": "sensor_data"}, "options": {"queryName": "match_sensor_data"}}, "id": "a54f1830-8948-4ad0-a034-61217076b485", "name": "Supabase Vector Store", "type": "@n8n/n8n-nodes-langchain.vectorStoreSupabase", "typeVersion": 1, "position": [1940, 600], "credentials": {"supabaseApi": {"id": "hOLIm3Jeg9JcG616", "name": "Prod Supabase account"}}}, {"parameters": {"operation": "text", "options": {}}, "type": "n8n-nodes-base.extractFromFile", "typeVersion": 1, "position": [1460, 1000], "id": "50e2dea0-ab4b-427d-9824-3b3c58488321", "name": "Extract from File"}, {"parameters": {"authentication": "serviceAccount", "operation": "download", "fileId": {"__rl": true, "value": "={{ $('Set Doc ID').item.json.file_id }}", "mode": "id"}, "options": {"googleFileConversion": {"conversion": {"docsToFormat": "text/plain"}}}}, "id": "f920fe2d-393b-4e4b-ba6f-3fae9f6b49c5", "name": "Download Text", "type": "n8n-nodes-base.googleDrive", "typeVersion": 3, "position": [1160, 1000], "executeOnce": true, "credentials": {"googleApi": {"id": "NTTcnCqQgvFST9F0", "name": "Farm Google Drive Account"}}}, {"parameters": {"assignments": {"assignments": [{"id": "9a9a245e-f1a1-4282-bb02-a81ffe629f0f", "name": "query", "value": "={{ {\n\"type\": \"human\",\n\"content\": $json.body.query,\n} }}", "type": "string"}, {"id": "b80831d8-c653-4203-8706-adedfdb98f77", "name": "user_id", "value": "={{ $json.body.user_id }}", "type": "string"}, {"id": "ea6f016e-2904-4701-8d8d-acabc83c705a", "name": "request_id", "value": "={{ $json.body.request_id }}", "type": "string"}, {"id": "aca1a7d0-4b20-4b45-abb5-2c2d16c7e5f6", "name": "session_id", "value": "={{ $json.body.session_id }}", "type": "string"}]}, "options": {}}, "id": "004fc62d-46f2-4a61-9c60-b8338429bf97", "name": "Prep Input Fields", "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [-220, 420]}, {"parameters": {"tableId": "messages", "fieldsUi": {"fieldValues": [{"fieldId": "session_id", "fieldValue": "={{ $json.session_id }}"}, {"fieldId": "message", "fieldValue": "={{ JSON.parse($json.query) }}"}]}}, "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [40, 360], "id": "da69e927-1274-40bf-8ba5-e659be70a0f0", "name": "Add User Message to DB", "credentials": {"supabaseApi": {"id": "hOLIm3Jeg9JcG616", "name": "Prod Supabase account"}}}, {"parameters": {"tableId": "messages", "fieldsUi": {"fieldValues": [{"fieldId": "session_id", "fieldValue": "={{ $('Prep Input Fields').item.json.session_id }}"}, {"fieldId": "message", "fieldValue": "={{ {\n\"type\": \"ai\",\n\"content\": $json.output,\n\"data\": $json.data,\n\"additional_kwargs\": {},\n\"response_metadata\": {}\n} }}"}]}}, "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [1660, 120], "id": "d7c9db41-f62b-4a14-afec-db7200382126", "name": "Add AI Message to DB", "credentials": {"supabaseApi": {"id": "hOLIm3Jeg9JcG616", "name": "Prod Supabase account"}}}, {"parameters": {"assignments": {"assignments": [{"id": "b9b7b266-3a46-4f72-96b7-90fce238f280", "name": "success", "value": "=true", "type": "boolean"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [1940, 120], "id": "765a5b1b-7a97-46fb-9df1-98ac9845b5ed", "name": "Prep Output Fields"}, {"parameters": {"assignments": {"assignments": [{"id": "10646eae-ae46-4327-a4dc-9987c2d76173", "name": "file_id", "value": "={{ $json.id }}", "type": "string"}, {"id": "f4536df5-d0b1-4392-bf17-b8137fb31a44", "name": "file_type", "value": "={{ $json.mimeType }}", "type": "string"}]}, "options": {}}, "id": "16b836d1-03fb-4d5e-b37b-159dbf027557", "name": "Set Doc ID", "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [680, 1000]}, {"parameters": {"name": "Generate_Chart", "description": "Call this tool when necessary to create a chart or for visualizations of any manner. All string values must be properly escaped, especially for multi-line strings. When processing the output of the tool, URLs returned should always be in markdown format. For example, ![](url)", "workflowId": {"__rl": true, "value": "={{ $workflow.id }}", "mode": "id"}, "workflowInputs": {"mappingMode": "defineBelow", "value": {"chatInput": "={{ (JSON.parse($('Prep Input Fields').item.json.query)).content }}"}, "matchingColumns": ["description"], "schema": [{"id": "chatInput", "displayName": "chatInput", "required": false, "defaultMatch": false, "display": true, "canBeUsedToMatch": true, "type": "string", "removed": false}], "attemptToConvertTypes": false, "convertFieldsToString": false}}, "type": "@n8n/n8n-nodes-langchain.toolWorkflow", "typeVersion": 2, "position": [1240, 580], "id": "f907254f-5c7e-4da8-9e58-61dc96ccf073", "name": "Chart Greenhouse Data"}, {"parameters": {"assignments": {"assignments": [{"id": "8346101e-afeb-4093-8727-517d69ea140c", "name": "response", "value": "={{ \"https://quickchart.io/chart?width=400&c=\" + encodeURIComponent(JSON.stringify($json.output)) }}\n", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [1260, 1260], "id": "360dadc7-7c66-4376-a8a0-dad73b7e699c", "name": "Query <PERSON>"}, {"parameters": {"assignments": {"assignments": [{"id": "0a61ce99-17b5-45a9-b258-3f0291f11ba1", "name": "chatInput", "value": "={{ $json?.chatInput || $json.body.chatInput }}", "type": "string"}, {"id": "db780496-3d7a-4049-9c0d-9fb99a9a87df", "name": "sessionId", "value": "={{ $json?.sessionId || $json.body.sessionId }}", "type": "string"}, {"id": "8df34e4d-3300-4d59-b4ed-15d3ae536408", "name": "table_id", "value": "myqybv0dxnx2qt8", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [260, 240], "id": "d6a6d27e-dc0f-46a4-bba4-8823f4b89be7", "name": "Settings"}, {"parameters": {"authentication": "nocoDbApiToken", "operation": "getAll", "workspaceId": "w33yjbmy", "projectId": "pa8jntwkscv3cqu", "table": "={{ $('Settings').item.json.table_id }}", "returnAll": true, "options": {}}, "type": "n8n-nodes-base.nocoDbTool", "typeVersion": 3, "position": [1100, 580], "id": "b85f5c0d-fc72-459e-a88a-b6cbf813c918", "name": "NocoDB", "credentials": {"nocoDbApiToken": {"id": "XqXxvBU3A4gbHQNR", "name": "Farming Token"}}}, {"parameters": {"assignments": {"assignments": [{"id": "0f09fcf1-60ff-467a-9b1f-0fd0ed2c28ab", "name": "columns", "value": "={{ $json.columns.map(item => item.title).toJsonString() }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [720, 240], "id": "f354e19d-7949-4996-9543-66386955bee5", "name": "Extract Columns"}, {"parameters": {"url": "=https://app.nocodb.com/api/v2/meta/tables/{{ $json.table_id }}", "authentication": "predefinedCredentialType", "nodeCredentialType": "nocoDbApiToken", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [480, 240], "id": "02a00289-6283-4d3c-8030-99dcc46e3faa", "name": "Extract Table", "credentials": {"nocoDbApiToken": {"id": "XqXxvBU3A4gbHQNR", "name": "Farming Token"}}}, {"parameters": {"inputSource": "jsonExample", "jsonExample": "{\n  \"chatInput\": \"a string\"\n}"}, "type": "n8n-nodes-base.executeWorkflowTrigger", "typeVersion": 1.1, "position": [440, 1260], "id": "0c57f473-7b35-406f-bc65-825cfdb6524a", "name": "Execute 'Generate a chart' tool"}, {"parameters": {"hasOutputParser": true, "options": {"systemMessage": "You are a Chart.js visualization agent tasked with generating accurate, well-labeled, and valid Chart.js configuration objects based on user requests. Your output should be immediately usable within a Chart.js environment to create insightful visualizations.\n\n1. Data Source:\nAlways use the NocoDB tool to retrieve real, live data from the relevant greenhouse tables in the NocoDB database. The data will be from the environmental sensors in the Coastal Greenhouse and Tropical Greenhouse. The required data points include:\n\nhumidity (%)\ntemp (°C)\npressure (hPa)\nbattery (V)\ntimestamp (CST) (format: YYYY-MM-DD HH:mm:ss)\nThe data should always be fetched from the database, and the requested chart must reflect this real-time data.\n\n2. Data Processing:\n\nRetrieve the data from the NocoDB API by querying the appropriate table(s) and columns.\nEnsure the data is sorted by timestamp (CST) in ascending order to ensure chronological accuracy.\nProcess the data into the appropriate format (for example, transforming timestamps into labels and sensor readings into datasets).\n3. Data Validation:\n\nEnsure that the fetched data has timestamps within the requested range.\nIf any labels or data are missing or inconsistent, prompt for clarification.\n4. Chart Generation:\nWhen the chart is requested, fetch data from NocoDB and then generate the chart configuration object based on the following parameters:\n\nChart Type (Line, Bar, Pie, etc.)\nAxis Titles (e.g., Timestamp (CST), Humidity (%))\nDatasets (use the real data for sensor readings)\nStyling Options (such as colors, gridlines, and tooltips)\nResponsive Design (ensure the chart is responsive and well-scaled for various devices)\nExample Request:\n\"Create a line chart showing humidity trends in the Coastal Greenhouse for the past week. Use the timestamp (CST) for the x-axis and humidity (%) for the y-axis. Title the chart 'Weekly Humidity Trends – Coastal Greenhouse' and make it visually engaging with smooth lines.\"\n\nExpected Chart Configuration (JSON):\n{\n  \"type\": \"line\",\n  \"data\": {\n    \"labels\": [\"2025-01-21 00:00:00\", \"2025-01-22 00:00:00\", \"2025-01-23 00:00:00\", \"2025-01-24 00:00:00\", \"2025-01-25 00:00:00\"],\n    \"datasets\": [\n      {\n        \"label\": \"Humidity (%)\",\n        \"data\": [60.5, 62.3, 58.9, 63.1, 61.7],\n        \"borderColor\": \"rgb(75, 192, 192)\",\n        \"backgroundColor\": \"rgba(75, 192, 192, 0.2)\",\n        \"tension\": 0.4,\n        \"fill\": true\n      }\n    ]\n  },\n  \"options\": {\n    \"responsive\": true,\n    \"plugins\": {\n      \"title\": {\n        \"display\": true,\n        \"text\": \"Weekly Humidity Trends – Coastal Greenhouse\"\n      }\n    },\n    \"scales\": {\n      \"x\": {\n        \"type\": \"time\",\n        \"time\": {\n          \"unit\": \"day\",\n          \"tooltipFormat\": \"YYYY-MM-DD HH:mm:ss\"\n        },\n        \"title\": {\n          \"display\": true,\n          \"text\": \"Timestamp (CST)\"\n        }\n      },\n      \"y\": {\n        \"title\": {\n          \"display\": true,\n          \"text\": \"Humidity (%)\"\n        },\n        \"beginAtZero\": true\n      }\n    }\n  }\n}\n"}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.7, "position": [800, 1260], "id": "17b64a0a-4ee9-460a-92f5-7d3ebe43a7ac", "name": "Charting Agent", "onError": "continueRegularOutput"}, {"parameters": {"promptType": "define", "text": "={{ $('Prep Input Fields').item.json.query }}", "options": {"systemMessage": "=You are a helpful and intelligent greenhouse data analysis agent that is skilled at answering questions about a live greenhouse table using the tools at your disposal. \n\nWhen answering user queries, provide direct and concise responses in full sentences rather than short lists. Keep your tone clear and natural, ensuring readability while maintaining brevity. Avoid unnecessary explanations or confirmations. Only include relevant details related to the user's request.\n\nWhen asked for calculations, return only the final result unless the user specifically requests details. For example, \"the average temperature in the Tropical Greenhouse is 21.14°C (70.05°F) based on 23 readings.\"\n\nYour goal is to help the user understand the greenhouse table at your disposal. Keep responses concise and only provide additional explanation if the user asks. Assume you already have all of the data and tools available to analyze.\n\nHere are the columns for that table.\n{{ $json.columns }}\n\nNote: the units for battery are in volts (V)\n\nThe greenhouses you support are:\n\nTropical Greenhouse: Simulating the highland tropical climate of the Kibara Plateau, DRC. This environment supports species such as the critically endangered Drosera Katangensis, which experiences seasonal dormancy during colder, drier conditions.\n\nCoastal Greenhouse: Designed to simulate coastal Mediterranean conditions, this greenhouse focuses on plants adapted to more temperate environments, with fluctuating humidity and milder temperature ranges.\n\nClearly indicate any limitations in data availability or scope."}}, "id": "7746d35e-0e93-4f0a-bed8-b5e3e3b70120", "name": "RAG AI Farming Agent", "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.6, "position": [1080, 240], "alwaysOutputData": false, "onError": "continueRegularOutput"}, {"parameters": {"options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.1, "position": [780, 1500], "id": "989f19ad-919b-418b-9f83-8a9b5e302d83", "name": "OpenAI Chat Model2", "credentials": {"openAiApi": {"id": "xAeHxzxTT16sMdwS", "name": "Backup OpenAI Account"}}}, {"parameters": {"options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatAnthropic", "typeVersion": 1.2, "position": [820, 580], "id": "57cc6147-80db-4cf7-b3eb-9a1c3fed2fd2", "name": "Anthropic <PERSON>", "credentials": {"anthropicApi": {"id": "AiDvkdxUFBeRQmnE", "name": "Anthropic account"}}}, {"parameters": {"toolDescription": "This HTTP request updates the threshold values for a specific greenhouse (Coastal Greenhouse or Tropical Greenhouse) for a specific metric (either humidity or temperature). The request is triggered when a user wants to set or update the threshold values for a greenhouse. The request requires the greenhouse name, the metric (humidity or temperature), and the threshold values (low and/or high) to be specified. \n\nIf the user provides the temperature in Fahrenheit (F), make sure to convert it to Celsius (C) using this formula: 1.8*((temperature in fahrenheit) - 32) before sending it to the POST endpoint.\n\nRequired Parameters:\nname (string): The name of the greenhouse (e.g., \"Tropical Greenhouse\" or \"Coastal Greenhouse\").\nmetric (string): The type of metric to update. Valid values are:\n\"humidity\": Refers to the humidity level in the greenhouse.\n\"temperature\": Refers to the temperature level in the greenhouse.\nlow (optional, integer): The lower threshold value. Should be defined if high is specified.\nhigh (optional, integer): The higher threshold value. Should be defined if low is specified.\nLogic:\nAt least one of low or high must be defined.\nIf both low and high are provided, low must be strictly less than high.\nResponse Handling:\nIf the response contains a \"message\" with \"Thresholds updated successfully\", the agent should notify the user that the update was successful and provide the discord link.\n\nIf the response contains an \"error\", the agent should relay the error message to the user, explaining what went wrong (e.g., invalid greenhouse name, missing parameters, or invalid threshold values).\nExample success response:\n{\n  \"message\": \"Thresholds updated successfully\",\n  \"data\": {\n    \"name\": \"Tropical Greenhouse\",\n    \"metric\": \"humidity\",\n    \"low\": 20,\n    \"high\": 30,\n    \"discord\": \"https://discord.gg/wVcxdFPf28\"\n  }\n}\n\n\nThe agent should respond to the user: \"The thresholds for Tropical Greenhouse have been updated successfully. Metric: humidity, Low: 20%, High: 30%.\" To check out the event updates, visit the discord integration here: https://discord.gg/wVcxdFPf28\nExample error response:\n{\n  \"error\": \"At least one of 'low' or 'high' must be defined\"\n}\n\n\nThe agent should respond to the user: \"Error: At least one of 'low' or 'high' must be defined.\"\n", "method": "POST", "url": "https://canopyserver.alurellc.com/discord/update_threshold", "sendBody": true, "parametersBody": {"values": [{"name": "name"}, {"name": "low", "valueProvider": "modelOptional"}, {"name": "high", "valueProvider": "modelOptional"}, {"name": "metric"}]}, "optimizeResponse": true}, "type": "@n8n/n8n-nodes-langchain.toolHttpRequest", "typeVersion": 1.1, "position": [1400, 580], "id": "f65d64a4-901e-4cc3-8a79-878db540d08c", "name": "<PERSON>"}, {"parameters": {"pollTimes": {"item": [{"mode": "everyMinute"}]}, "authentication": "serviceAccount", "triggerOn": "specificFile", "fileToWatch": {"__rl": true, "value": "188bjeoQsruiv54pRbcU-TZ6jGdcYB_9TgWJNWyaV3gc", "mode": "list", "cachedResultName": "Greenhouse Documentation", "cachedResultUrl": "https://docs.google.com/document/d/188bjeoQsruiv54pRbcU-TZ6jGdcYB_9TgWJNWyaV3gc/edit?usp=drivesdk"}}, "type": "n8n-nodes-base.googleDriveTrigger", "typeVersion": 1, "position": [440, 1000], "id": "5c76ad6e-ed3f-4cc0-89d9-37610fd074c1", "name": "Google Drive Trigger", "credentials": {"googleApi": {"id": "NTTcnCqQgvFST9F0", "name": "Farm Google Drive Account"}}}, {"parameters": {"toolDescription": "Gets the most recent image of a top view snapshot from the coastal greenhouse. Trigger this tool whenever the user asks about the current image or view in the coastal greenhouse. If they ask about the tropical greenhouse view, kindly explain that there isn't a mini-camera hooked up in that greenhouse yet.\n\nThe tool makes a GET request to https://canopyserver.alurellc.com/get_image and returns an image_url and timestamp in the response, similar to:\n\n{\n    \"image_url\": \"/images/camera_image_latest.jpg\",\n    \"timestamp\": \"20250128_065508\"\n}\nAfter receiving the response:\n\nDecode the timestamp and return it in a user-friendly format (e.g., \"January 28, 2025, 06:55:08 AM CST\"). The timestamps are in CST, so be sure to specify that. \n\nFormat the image_url as a full URL (https://canopyserver.alurellc.com{image_url}).\nReturn the image URL in markdown format like ![](https://canopyserver.alurellc.com/images/camera_image_latest.jpg) to allow users to view the image.\n\nIf there's an error or the timestamp can't be decoded, respond with a user-friendly explanation.\n\nExample Output in Markdown: \nAfter making the call, the tool would return something like this to the user:\n\nTimestamp: January 28, 2025, 06:55:08 AM (specify CST)\nImage: ![](https://canopyserver.alurellc.com/images/camera_image_latest.jpg)\nThis approach ensures the image can be presented cleanly in markdown format while decoding the timestamp for user clarity.", "url": "https://canopyserver.alurellc.com/get_image", "optimizeResponse": true}, "type": "@n8n/n8n-nodes-langchain.toolHttpRequest", "typeVersion": 1.1, "position": [1560, 580], "id": "0f6f8333-aa1d-4461-b834-be11dee114e8", "name": "Get Greenhouse Image"}, {"parameters": {"authentication": "nocoDbApiToken", "operation": "getAll", "workspaceId": "w33yjbmy", "projectId": "pa8jntwkscv3cqu", "table": "myqybv0dxnx2qt8", "returnAll": true, "options": {}}, "type": "n8n-nodes-base.nocoDbTool", "typeVersion": 3, "position": [920, 1480], "id": "ffa3286b-6f54-4fb2-be90-2e1853461e2a", "name": "NocoDB1", "credentials": {"nocoDbApiToken": {"id": "XqXxvBU3A4gbHQNR", "name": "Farming Token"}}}, {"parameters": {"schemaType": "manual", "inputSchema": "{\n  \"properties\": {\n    \"options\": {\n      \"type\": \"object\",\n      \"properties\": {\n        \"responsive\": {\n          \"type\": \"boolean\"\n        },\n        \"maintainAspectRatio\": {\n          \"type\": \"boolean\"\n        },\n        \"scales\": {\n          \"type\": \"object\",\n          \"properties\": {\n            \"x\": {\n              \"type\": \"object\",\n              \"properties\": {\n                \"display\": {\n                  \"type\": \"boolean\"\n                },\n                \"title\": {\n                  \"type\": \"object\",\n                  \"properties\": {\n                    \"display\": {\n                      \"type\": \"boolean\"\n                    },\n                    \"text\": {\n                      \"type\": \"string\"\n                    }\n                  }\n                }\n              }\n            },\n            \"y\": {\n              \"type\": \"object\",\n              \"properties\": {\n                \"display\": {\n                  \"type\": \"boolean\"\n                },\n                \"title\": {\n                  \"type\": \"object\",\n                  \"properties\": {\n                    \"display\": {\n                      \"type\": \"boolean\"\n                    },\n                    \"text\": {\n                      \"type\": \"string\"\n                    }\n                  }\n                }\n              }\n            }\n          }\n        },\n        \"plugins\": {\n          \"type\": \"object\",\n          \"properties\": {\n            \"legend\": {\n              \"type\": \"object\",\n              \"properties\": {\n                \"display\": {\n                  \"type\": \"boolean\"\n                },\n                \"position\": {\n                  \"type\": \"string\",\n                  \"enum\": [\"top\", \"left\", \"bottom\", \"right\"]\n                }\n              }\n            },\n            \"tooltip\": {\n              \"type\": \"object\",\n              \"properties\": {\n                \"enabled\": {\n                  \"type\": \"boolean\"\n                }\n              }\n            }\n          }\n        },\n        \"animation\": {\n          \"type\": \"object\",\n          \"properties\": {\n            \"duration\": {\n              \"type\": \"number\"\n            },\n            \"easing\": {\n              \"type\": \"string\",\n              \"enum\": [\"linear\", \"easeInOutQuad\", \"easeInQuad\", \"easeOutQuad\", \"easeInOutCubic\", \"easeInCubic\", \"easeOutCubic\"]\n            }\n          }\n        }\n      }\n    }\n  }\n}\n"}, "type": "@n8n/n8n-nodes-langchain.outputParserStructured", "typeVersion": 1.2, "position": [1040, 1480], "id": "68cbb901-f1bd-45eb-bf8d-74915f76421d", "name": "Structured Output Parser"}], "pinData": {"Webhook": [{"json": {"headers": {"host": "n8n.[your n8n url].com", "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "content-length": "192", "accept": "*/*", "accept-encoding": "deflate, gzip", "authorization": "Bearer YOUR BEARER TOKEN", "content-type": "application/json", "x-forwarded-for": "*************", "x-forwarded-host": "n8n.[your n8n url].com", "x-forwarded-proto": "https", "x-real-ip": "2601:441:4380:40b0:b4b3:724b:27e1:c4ba"}, "params": {}, "query": {}, "body": {"query": "Supabase", "user_id": "google-oauth2|116467443974012389959", "request_id": "f98asdyf987yasd0f987asdf8", "session_id": "google-oauth2|116467443974012389959~2~8dfbddbe603d"}, "webhookUrl": "https://n8n.[your n8n url].com/webhook-test/invoke-agent", "executionMode": "test"}}]}, "connections": {"OpenAI Chat Model1": {"ai_languageModel": [[{"node": "Retrieve Documents", "type": "ai_languageModel", "index": 0}]]}, "Retrieve Documents": {"ai_tool": [[{"node": "RAG AI Farming Agent", "type": "ai_tool", "index": 0}]]}, "Embeddings OpenAI": {"ai_embedding": [[{"node": "Supabase Vector Store", "type": "ai_embedding", "index": 0}]]}, "Embeddings OpenAI1": {"ai_embedding": [[{"node": "Insert into Supabase Vectorstore", "type": "ai_embedding", "index": 0}]]}, "Default Data Loader": {"ai_document": [[{"node": "Insert into Supabase Vectorstore", "type": "ai_document", "index": 0}]]}, "Postgres Chat Memory": {"ai_memory": [[{"node": "RAG AI Farming Agent", "type": "ai_memory", "index": 0}]]}, "Delete Old Doc Rows": {"main": [[{"node": "Download Text", "type": "main", "index": 0}]]}, "When chat message received": {"main": [[{"node": "Settings", "type": "main", "index": 0}]]}, "Webhook": {"main": [[{"node": "Prep Input Fields", "type": "main", "index": 0}]]}, "Character Text Splitter": {"ai_textSplitter": [[{"node": "Default Data Loader", "type": "ai_textSplitter", "index": 0}]]}, "Supabase Vector Store": {"ai_vectorStore": [[{"node": "Retrieve Documents", "type": "ai_vectorStore", "index": 0}]]}, "Extract from File": {"main": [[{"node": "Insert into Supabase Vectorstore", "type": "main", "index": 0}]]}, "Download Text": {"main": [[{"node": "Extract from File", "type": "main", "index": 0}]]}, "Prep Input Fields": {"main": [[{"node": "Add User Message to DB", "type": "main", "index": 0}]]}, "Add AI Message to DB": {"main": [[{"node": "Prep Output Fields", "type": "main", "index": 0}]]}, "Prep Output Fields": {"main": [[{"node": "Respond to Webhook", "type": "main", "index": 0}]]}, "Set Doc ID": {"main": [[{"node": "Delete Old Doc Rows", "type": "main", "index": 0}]]}, "Chart Greenhouse Data": {"ai_tool": [[{"node": "RAG AI Farming Agent", "type": "ai_tool", "index": 0}]]}, "Settings": {"main": [[{"node": "Extract Table", "type": "main", "index": 0}]]}, "NocoDB": {"ai_tool": [[{"node": "RAG AI Farming Agent", "type": "ai_tool", "index": 0}]]}, "Extract Columns": {"main": [[{"node": "RAG AI Farming Agent", "type": "main", "index": 0}]]}, "Extract Table": {"main": [[{"node": "Extract Columns", "type": "main", "index": 0}]]}, "Execute 'Generate a chart' tool": {"main": [[{"node": "Charting Agent", "type": "main", "index": 0}]]}, "Charting Agent": {"main": [[{"node": "Query <PERSON>", "type": "main", "index": 0}]]}, "RAG AI Farming Agent": {"main": [[{"node": "Add AI Message to DB", "type": "main", "index": 0}]]}, "OpenAI Chat Model2": {"ai_languageModel": [[{"node": "Charting Agent", "type": "ai_languageModel", "index": 0}]]}, "Anthropic Chat Model": {"ai_languageModel": [[{"node": "RAG AI Farming Agent", "type": "ai_languageModel", "index": 0}]]}, "Set Alerts": {"ai_tool": [[{"node": "RAG AI Farming Agent", "type": "ai_tool", "index": 0}]]}, "Google Drive Trigger": {"main": [[{"node": "Set Doc ID", "type": "main", "index": 0}]]}, "Get Greenhouse Image": {"ai_tool": [[{"node": "RAG AI Farming Agent", "type": "ai_tool", "index": 0}]]}, "NocoDB1": {"ai_tool": [[{"node": "Charting Agent", "type": "ai_tool", "index": 0}]]}, "Add User Message to DB": {"main": [[{"node": "Settings", "type": "main", "index": 0}]]}, "Structured Output Parser": {"ai_outputParser": [[{"node": "Charting Agent", "type": "ai_outputParser", "index": 0}]]}}, "active": true, "settings": {"executionOrder": "v1"}, "versionId": "8e0fbd58-b3ff-4c62-9041-dd4bae87dbfc", "meta": {"templateCredsSetupCompleted": true, "instanceId": "f65a08c0adc90a3cde2c633d24c6daecde3817033b75588ee10a781b0b7aa3f5"}, "id": "KzdWiRlX9PqcJOBh", "tags": [{"createdAt": "2024-12-10T13:21:06.912Z", "updatedAt": "2024-12-10T13:21:06.912Z", "id": "0tXJXfH2daB7QdK5", "name": "studio-test"}]}