I want you to build me an army of AI agents. You'll have one primary agent that then calls upon these subagents to perform actions based on the third party service it is an expert with:

Airtable subagent
Brave search subagent
File system subagent
GitHub subagent
Slack subagent
Firecrawl subagent

Implement each of these subagents at the top of the file for the primary agent and use the MCP server for each service (Air<PERSON>, Brave, Slack, etc.).

For the primary agent, implement a tool to call each of these subagents and make sure the docstring expresses when to call each subagent (<PERSON>lack to send messages, Brave for web search, etc.).
Don't have the tool actually start the MCP servers, you do not want to do that each time the tool is called since that is very slow.
use async with AsyncExitStack() as stack: and then enter_async_context for each of the subagents to get the MCP servers up and running. Then for the tool calls for the primary agent, it can be as simple as just running each agent.

Make sure the .env.example file covers all the necessary environment variables for the different MCP servers and that the MCP servers are set up to use the environment variables for the env parameter when applicable.