{"name": "bolt.diy <PERSON>pert", "nodes": [{"parameters": {"assignments": {"assignments": [{"id": "ee2bcd57-3b4c-43f9-b4b7-3a25687b9a68", "name": "query", "value": "={{ $json.body.query }}", "type": "string"}, {"id": "63f23e51-af2b-47c4-a288-5abaf9b6c357", "name": "user_id", "value": "={{ $json.body.user_id }}", "type": "string"}, {"id": "b97a3670-8a87-481b-8695-db44624be7d8", "name": "request_id", "value": "={{ $json.body.request_id }}", "type": "string"}, {"id": "b4a4acb2-b2c4-45c1-af2e-02636999af94", "name": "session_id", "value": "={{ $json.body.session_id }}", "type": "string"}]}, "options": {}}, "id": "dca2c9ed-5239-4625-b2c4-1d606d19dba5", "name": "<PERSON>", "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [920, 460]}, {"parameters": {"respondWith": "allIncomingItems", "options": {"responseHeaders": {"entries": [{"name": "X-n8n-Signature", "value": "EvtIS^EBVISeie6svB@6ev"}]}}}, "id": "4848fc52-dcfd-4c02-a475-2bb7ba52750a", "name": "Respond to Webhook", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1.1, "position": [1560, 460]}, {"parameters": {"sessionIdType": "customKey", "sessionKey": "={{ $('Edit Fields').item.json.session_id }}", "tableName": "messages"}, "id": "b72d76a2-86c9-48cd-b477-6fd9d927c698", "name": "Postgres Chat Memory", "type": "@n8n/n8n-nodes-langchain.memoryPostgresChat", "typeVersion": 1, "position": [1220, 680], "credentials": {"postgres": {"id": "erIa9T64hNNeDuvB", "name": "Prod Postgres account"}}}, {"parameters": {"options": {}}, "id": "697449d0-776f-410c-9f8a-39e7eeb2220b", "name": "OpenAI Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1, "position": [1080, 680], "credentials": {"openAiApi": {"id": "05Q6PbnSdyEcu9Ze", "name": "OpenAi account"}}}, {"parameters": {"url": "https://stackblitz-labs.github.io/bolt.diy/", "options": {}}, "id": "9253b800-e76b-4a84-9345-bda3ba183279", "name": "HTTP Request1", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [840, 920]}, {"parameters": {"operation": "extractHtmlContent", "extractionValues": {"values": [{"key": "links", "cssSelector": "nav a.md-nav__link", "returnValue": "attribute", "attribute": "href", "returnArray": true}]}, "options": {}}, "id": "f3d1ed14-cecd-405e-a13f-3285f5ef1a8b", "name": "HTML", "type": "n8n-nodes-base.html", "typeVersion": 1.2, "position": [1040, 920]}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "320649a3-9c46-4067-bdec-7566a3e588ed", "leftValue": "={{ $json.links }}", "rightValue": "/", "operator": {"type": "string", "operation": "endsWith"}}, {"id": "dbd44d34-9f7e-4eae-9e14-cd82a5b33387", "leftValue": "={{ $json.links }}", "rightValue": ".", "operator": {"type": "string", "operation": "equals"}}], "combinator": "or"}, "options": {}}, "id": "8be35a61-83e2-4daf-aa89-1339e6213891", "name": "Filter", "type": "n8n-nodes-base.filter", "typeVersion": 2, "position": [1400, 920]}, {"parameters": {"fieldToSplitOut": "links", "options": {}}, "id": "cbc5bc65-a07b-4f04-8eec-1ecc95472b83", "name": "Split Out", "type": "n8n-nodes-base.splitOut", "typeVersion": 1, "position": [1220, 920]}, {"parameters": {"url": "={{ `https://stackblitz-labs.github.io/bolt.diy/${$json.links.replaceAll(\".\", \"\")}` }}", "options": {}}, "id": "e0b26a8e-1a61-4921-9100-bb826fc8ac9a", "name": "HTTP Request2", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1580, 920]}, {"parameters": {"operation": "extractHtmlContent", "extractionValues": {"values": [{"key": "content", "cssSelector": "article", "returnArray": true}]}, "options": {}}, "id": "bf070d33-4bbe-4d51-8df6-86c6e6a74d89", "name": "HTML1", "type": "n8n-nodes-base.html", "typeVersion": 1.2, "position": [1780, 920]}, {"parameters": {"fieldsToSummarize": {"values": [{"aggregation": "concatenate", "field": "content", "separateBy": "other", "customSeparator": "\\n\\nNext page:\\n\\n"}]}, "options": {}}, "id": "414bcbde-4695-4d8d-b6b9-4c8728d9acd8", "name": "Summarize", "type": "n8n-nodes-base.summarize", "typeVersion": 1, "position": [1980, 920]}, {"parameters": {}, "id": "8c0645ff-19a5-4036-8852-21372c2221e1", "name": "Execute Workflow Trigger", "type": "n8n-nodes-base.executeWorkflowTrigger", "typeVersion": 1, "position": [420, 920]}, {"parameters": {"name": "fetch_ottodev_documentation", "description": "Call this tool to get the oTToDev documentation when you need to answer a question for the user.", "workflowId": {"__rl": true, "value": "={{ $workflow.id }}", "mode": "id"}, "responsePropertyName": "concatenated_content", "fields": {"values": [{"name": "session_id", "stringValue": "={{$('Edit Fields').item.json.session_id}}"}]}}, "id": "110b01dc-f42c-48d8-a13b-4fcba96eb92e", "name": "Call n8n Workflow Tool", "type": "@n8n/n8n-nodes-langchain.toolWorkflow", "typeVersion": 1.2, "position": [1360, 680]}, {"parameters": {"tableId": "messages", "fieldsUi": {"fieldValues": [{"fieldId": "session_id", "fieldValue": "={{ $json.session_id }}"}, {"fieldId": "message", "fieldValue": "={{ {\n\"type\": \"ai\",\n\"content\": \"-> Reading bolt.diy documentation...\",\n\"data\": {},\n\"additional_kwargs\": {},\n\"response_metadata\": {}\n} }}"}]}}, "id": "0f4ba406-3442-41b0-846a-31dbf8f6d56f", "name": "Add AI Documentation Message to DB", "type": "n8n-nodes-base.supabase", "typeVersion": 1, "position": [620, 920], "credentials": {"supabaseApi": {"id": "hOLIm3Jeg9JcG616", "name": "Prod Supabase account"}}}, {"parameters": {"content": "# bolt.diy Expert Agent\n\nAuthor: [<PERSON>](https://www.youtube.com/@ColeMedin)\n\nThe bolt.diy Expert Agent is a specialized n8n AI agent designed to assist users with questions and issues related to bolt.diy - the official open source bolt.new that enables users to choose from multiple LLM providers for web development. It does this by researching the bolt.diy documentation for the user.\n\n## Purpose\n\nThis agent serves as a knowledgeable assistant that:\n- Answers questions about bolt.diy features and capabilities\n- Helps troubleshoot installation and setup issues\n- Provides guidance on using different LLM providers\n- Assists with understanding bolt.diy architecture and components\n- Offers support for contributing to the project", "height": 441.**************, "width": 651.*************, "color": 6}, "id": "07422adc-27e3-4d6a-813e-4b34b58b4b65", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-80, 420]}, {"parameters": {"httpMethod": "POST", "path": "invoke-ottodev-expert", "authentication": "headerAuth", "responseMode": "responseNode", "options": {}}, "id": "c87f3c57-17bd-4c19-a178-af742e8b8686", "name": "Webhook", "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [680, 460], "webhookId": "ba5e9235-01af-47f8-9f72-29df862bfa0f", "credentials": {"httpHeaderAuth": {"id": "o5akNgXQQR74Sezh", "name": "Header Auth account"}}}, {"parameters": {"promptType": "define", "text": "={{ $('Edit Fields').item.json.query }}", "options": {"systemMessage": "=You are an expert at an open source AI coding assistant called bolt.diy. You also have access to fetch the full documentation for bolt.diy to answer any questions for the user. The documentation URL is:\n\nhttps://stackblitz-labs.github.io/bolt.diy\n\nYou use this README to answer user questions to the best of your ability. If you don't know the answer and the guide/FAQ doesn't help you with it, just be honest and tell the user you don't know the answer and they can instead post a question on the Discourse community for bolt.diy (the Think Tank):\n\nhttps://thinktank.ottomator.ai\n\nYour answers are focused around helping beginners use bolt.diy. Keep your answers simple yet still comprehensive!"}}, "id": "c62238b1-8dc2-41cf-a5f4-10f4be918ee1", "name": "AI Agent", "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.6, "position": [1160, 460]}], "pinData": {}, "connections": {"Edit Fields": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}]]}, "Postgres Chat Memory": {"ai_memory": [[{"node": "AI Agent", "type": "ai_memory", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}]]}, "HTTP Request1": {"main": [[{"node": "HTML", "type": "main", "index": 0}]]}, "HTML": {"main": [[{"node": "Split Out", "type": "main", "index": 0}]]}, "Split Out": {"main": [[{"node": "Filter", "type": "main", "index": 0}]]}, "Filter": {"main": [[{"node": "HTTP Request2", "type": "main", "index": 0}]]}, "HTTP Request2": {"main": [[{"node": "HTML1", "type": "main", "index": 0}]]}, "HTML1": {"main": [[{"node": "Summarize", "type": "main", "index": 0}]]}, "Execute Workflow Trigger": {"main": [[{"node": "Add AI Documentation Message to DB", "type": "main", "index": 0}]]}, "Call n8n Workflow Tool": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "Add AI Documentation Message to DB": {"main": [[{"node": "HTTP Request1", "type": "main", "index": 0}]]}, "Webhook": {"main": [[{"node": "<PERSON>", "type": "main", "index": 0}]]}, "AI Agent": {"main": [[{"node": "Respond to Webhook", "type": "main", "index": 0}]]}}, "active": true, "settings": {"executionOrder": "v1"}, "versionId": "c8db1a00-fffa-4760-8d11-4b5d51a780ff", "meta": {"templateCredsSetupCompleted": true, "instanceId": "f65a08c0adc90a3cde2c633d24c6daecde3817033b75588ee10a781b0b7aa3f5"}, "id": "u43sbX4DVrbxo1Hj", "tags": [{"createdAt": "2024-12-09T14:35:20.507Z", "updatedAt": "2024-12-09T14:35:20.507Z", "id": "wBUwbX8AS8QmBHOC", "name": "studio-prod"}]}