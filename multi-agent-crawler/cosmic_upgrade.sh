#!/bin/bash

# 🏠 Cosmic Quality Multi-Agent Crawler Upgrade Script
# Transforms the system to award-winning real estate intelligence platform

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Cosmic Quality Banner
print_cosmic_banner() {
    echo -e "${PURPLE}"
    echo "╔══════════════════════════════════════════════════════════════════════════════╗"
    echo "║                    🏠 COSMIC QUALITY UPGRADE SYSTEM                          ║"
    echo "║                                                                              ║"
    echo "║           Multi-Agent Real Estate Intelligence Platform                      ║"
    echo "║                    Award-Winning Data Collection                            ║"
    echo "║                   Corporation-Level Presentation                           ║"
    echo "║                                                                              ║"
    echo "║                        Upgrading to Cosmic Standards...                     ║"
    echo "╚══════════════════════════════════════════════════════════════════════════════╝"
    echo -e "${NC}"
}

# Logging function
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

warn() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}"
}

info() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')] INFO: $1${NC}"
}

cosmic() {
    echo -e "${PURPLE}[$(date +'%Y-%m-%d %H:%M:%S')] ✨ COSMIC: $1${NC}"
}

# Check system requirements
check_system_requirements() {
    log "🔍 Checking system requirements for Cosmic Quality standards..."
    
    # Check Docker
    if ! command -v docker &> /dev/null; then
        error "Docker is not installed. Please install Docker first."
        exit 1
    fi
    
    # Check Docker Compose
    if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
        error "Docker Compose is not installed. Please install Docker Compose first."
        exit 1
    fi
    
    # Check available memory
    TOTAL_MEM=$(free -g | awk '/^Mem:/{print $2}')
    if [ "$TOTAL_MEM" -lt 8 ]; then
        warn "System has less than 8GB RAM. Cosmic Quality requires at least 16GB for optimal performance."
    fi
    
    # Check available CPU cores
    CPU_CORES=$(nproc)
    if [ "$CPU_CORES" -lt 4 ]; then
        warn "System has less than 4 CPU cores. Cosmic Quality requires at least 4 vCPU for optimal performance."
    fi
    
    # Check disk space
    DISK_SPACE=$(df -BG . | awk 'NR==2 {print $4}' | sed 's/G//')
    if [ "$DISK_SPACE" -lt 20 ]; then
        warn "Less than 20GB disk space available. Consider freeing up space for optimal performance."
    fi
    
    cosmic "System requirements check completed"
}

# Create cosmic configuration directories
create_cosmic_directories() {
    log "📁 Creating Cosmic Quality configuration directories..."
    
    mkdir -p shared/nocodb-config
    mkdir -p shared/redis-config
    mkdir -p shared/crawl4ai-config
    mkdir -p shared/scheduler-config
    mkdir -p cosmic-config
    mkdir -p logs/cosmic
    
    cosmic "Configuration directories created"
}

# Generate cosmic configuration files
generate_cosmic_configs() {
    log "⚙️ Generating Cosmic Quality configuration files..."
    
    # Redis configuration
    cat > shared/redis-config/redis.conf << EOF
# Cosmic Quality Redis Configuration
maxmemory 2gb
maxmemory-policy allkeys-lru
save 900 1
save 300 10
save 60 10000
appendonly yes
appendfsync everysec
tcp-keepalive 300
timeout 0
databases 16
maxclients 10000

# Performance optimizations
tcp-backlog 511
timeout 0
tcp-keepalive 300
supervised no
pidfile /var/run/redis_6379.pid
loglevel notice
logfile ""
databases 16
always-show-logo yes
EOF

    # NocoDB configuration
    cat > shared/nocodb-config/config.json << EOF
{
  "cosmic_quality": true,
  "performance_mode": "optimized",
  "cache_enabled": true,
  "real_time_sync": true,
  "backup_interval": "1h",
  "max_connections": 100,
  "query_timeout": 30000,
  "memory_limit": "2GB"
}
EOF

    # Crawl4AI configuration
    cat > shared/crawl4ai-config/config.yaml << EOF
cosmic_quality:
  enabled: true
  target_accuracy: 0.999
  max_response_time_ms: 1000
  
browser:
  headless: true
  timeout: 120
  page_load_timeout: 60
  stealth_mode: true
  user_agent: "CosmicRealEstate/1.0 (Professional Real Estate Intelligence)"
  
extraction:
  timeout: 30
  enable_javascript: true
  enable_screenshots: true
  enable_pdf: true
  quality_level: "cosmic"
  
rate_limiting:
  delay_between_requests: 2
  respect_robots_txt: true
  max_concurrent: 3
EOF

    cosmic "Configuration files generated"
}

# Backup existing data
backup_existing_data() {
    log "💾 Creating backup of existing data..."
    
    BACKUP_DIR="backups/$(date +%Y%m%d_%H%M%S)"
    mkdir -p "$BACKUP_DIR"
    
    # Backup Docker volumes if they exist
    if docker volume ls | grep -q "multi-agent-crawler"; then
        info "Backing up existing Docker volumes..."
        docker run --rm -v multi-agent-crawler_nocodb_data:/source -v "$(pwd)/$BACKUP_DIR":/backup alpine tar czf /backup/nocodb_data.tar.gz -C /source .
        docker run --rm -v multi-agent-crawler_redis_data:/source -v "$(pwd)/$BACKUP_DIR":/backup alpine tar czf /backup/redis_data.tar.gz -C /source .
        docker run --rm -v multi-agent-crawler_crawler_data:/source -v "$(pwd)/$BACKUP_DIR":/backup alpine tar czf /backup/crawler_data.tar.gz -C /source .
    fi
    
    # Backup configuration files
    if [ -f ".env" ]; then
        cp .env "$BACKUP_DIR/"
    fi
    
    cosmic "Backup completed in $BACKUP_DIR"
}

# Stop existing services
stop_existing_services() {
    log "🛑 Stopping existing services..."
    
    if docker-compose ps | grep -q "Up"; then
        docker-compose down
    fi
    
    cosmic "Existing services stopped"
}

# Update package requirements
update_package_requirements() {
    log "📦 Updating package requirements to latest versions..."
    
    # Update main requirements
    cat > crawler-orchestrator/requirements.txt << EOF
# Core dependencies - Latest versions
fastapi==0.115.12
uvicorn[standard]==0.32.1
pydantic==2.10.3
python-dotenv==1.0.1

# CrewAI and agents - Latest versions
crewai==0.126.0
crewai-tools==0.17.0
langchain==0.3.12
langchain-openai==0.2.14

# Web crawling and scraping - Enhanced
crawl4ai==0.6.3
playwright==1.52.0
beautifulsoup4==4.12.3
requests==2.32.3
httpx==0.27.2

# Search APIs - Latest
exa-py==1.1.0
brave-search==0.1.8

# Database and storage - Enhanced
redis==5.2.1
aioredis==2.0.1
sqlalchemy==2.0.36
alembic==1.14.0
asyncpg==0.30.0

# Data processing - Latest
pandas==2.2.3
numpy==2.2.1
python-multipart==0.0.12

# UI and visualization - Latest
gradio==5.9.1
streamlit==1.41.0
plotly==5.24.1
plotly-express==0.4.1
python-pptx==0.6.23

# Utilities - Latest
celery==5.4.0
schedule==1.2.2
python-crontab==3.2.0
loguru==0.7.2

# Testing - Latest
pytest==8.3.4
pytest-asyncio==1.0.0

# Cosmic Quality additions
psutil==6.1.1
jinja2==3.1.4
aiofiles==22.1.0
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
EOF

    cosmic "Package requirements updated to latest versions"
}

# Build cosmic quality images
build_cosmic_images() {
    log "🏗️ Building Cosmic Quality Docker images..."
    
    # Build with no cache to ensure latest packages
    docker-compose build --no-cache --parallel
    
    cosmic "Cosmic Quality images built successfully"
}

# Start cosmic services
start_cosmic_services() {
    log "🚀 Starting Cosmic Quality services..."
    
    # Start services in order
    docker-compose up -d redis nocodb
    sleep 10
    
    docker-compose up -d crawl4ai scheduler
    sleep 10
    
    docker-compose up -d crawler-orchestrator
    
    cosmic "Cosmic Quality services started"
}

# Verify cosmic deployment
verify_cosmic_deployment() {
    log "✅ Verifying Cosmic Quality deployment..."
    
    # Wait for services to be ready
    sleep 30
    
    # Check service health
    SERVICES=("8000" "8001" "8002" "8003" "8004" "8080" "6379")
    SERVICE_NAMES=("FastAPI" "Gradio" "Crawl4AI" "Streamlit" "Tablet" "NocoDB" "Redis")
    
    for i in "${!SERVICES[@]}"; do
        PORT="${SERVICES[$i]}"
        NAME="${SERVICE_NAMES[$i]}"
        
        if curl -s -f "http://localhost:$PORT" > /dev/null 2>&1 || \
           curl -s -f "http://localhost:$PORT/health" > /dev/null 2>&1; then
            cosmic "✅ $NAME service is running on port $PORT"
        else
            warn "⚠️ $NAME service may not be ready on port $PORT"
        fi
    done
    
    cosmic "Deployment verification completed"
}

# Display cosmic success message
display_cosmic_success() {
    echo -e "${PURPLE}"
    echo "╔══════════════════════════════════════════════════════════════════════════════╗"
    echo "║                    🎉 COSMIC QUALITY UPGRADE COMPLETE! 🎉                   ║"
    echo "║                                                                              ║"
    echo "║  Your Multi-Agent Real Estate Intelligence Platform is now running with     ║"
    echo "║                        COSMIC QUALITY STANDARDS                             ║"
    echo "║                                                                              ║"
    echo "║  🌐 Access Points:                                                          ║"
    echo "║     • FastAPI REST API:        http://localhost:8000                       ║"
    echo "║     • Gradio Interface:        http://localhost:8001                       ║"
    echo "║     • Crawl4AI Service:        http://localhost:8002                       ║"
    echo "║     • Streamlit Control:       http://localhost:8003                       ║"
    echo "║     • Tablet Interface:        http://localhost:8004                       ║"
    echo "║     • Cosmic Streamlit:        http://localhost:8005                       ║"
    echo "║     • NocoDB Database:         http://localhost:8080                       ║"
    echo "║                                                                              ║"
    echo "║  🏆 Features Enabled:                                                       ║"
    echo "║     ✅ Award-winning data collection                                        ║"
    echo "║     ✅ Corporation-level presentation                                       ║"
    echo "║     ✅ Real estate specialized agents                                       ║"
    echo "║     ✅ Tablet-optimized interface                                          ║"
    echo "║     ✅ Performance monitoring                                               ║"
    echo "║     ✅ Redis 7+ caching                                                     ║"
    echo "║     ✅ NocoDB latest version                                                ║"
    echo "║                                                                              ║"
    echo "║                    Ready for Cosmic Quality Operations! 🚀                 ║"
    echo "╚══════════════════════════════════════════════════════════════════════════════╝"
    echo -e "${NC}"
}

# Main upgrade function
main() {
    print_cosmic_banner
    
    log "🚀 Starting Cosmic Quality upgrade process..."
    
    check_system_requirements
    create_cosmic_directories
    generate_cosmic_configs
    backup_existing_data
    stop_existing_services
    update_package_requirements
    build_cosmic_images
    start_cosmic_services
    verify_cosmic_deployment
    
    display_cosmic_success
    
    cosmic "🎉 Cosmic Quality upgrade completed successfully!"
    info "📖 Check the logs with: docker-compose logs -f"
    info "📊 Monitor performance with: python monitor_performance.py"
}

# Handle script arguments
case "${1:-}" in
    "start")
        log "🚀 Starting Cosmic Quality services..."
        docker-compose up -d
        ;;
    "stop")
        log "🛑 Stopping Cosmic Quality services..."
        docker-compose down
        ;;
    "restart")
        log "🔄 Restarting Cosmic Quality services..."
        docker-compose restart
        ;;
    "logs")
        log "📖 Showing Cosmic Quality logs..."
        docker-compose logs -f
        ;;
    "status")
        log "📊 Checking Cosmic Quality status..."
        docker-compose ps
        ;;
    "clean")
        log "🧹 Cleaning up Cosmic Quality resources..."
        docker-compose down -v
        docker system prune -f
        ;;
    "monitor")
        log "📊 Starting performance monitoring..."
        python monitor_performance.py
        ;;
    *)
        main
        ;;
esac
