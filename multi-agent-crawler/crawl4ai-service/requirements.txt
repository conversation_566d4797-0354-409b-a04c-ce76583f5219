# Core dependencies - Latest stable versions
fastapi==0.115.9
uvicorn[standard]==0.34.2
pydantic==2.11.4
pydantic-settings==2.9.1
python-dotenv==1.1.0

# Web crawling and browser automation - Compatible versions
crawl4ai==0.6.2
playwright==1.52.0
beautifulsoup4==4.13.4
requests==2.32.3
httpx==0.28.1
aiohttp==3.11.18
lxml==5.4.0

# Async and concurrency - Resolved conflicts
aiofiles==24.1.0
aiohappyeyeballs==2.6.1
aiosignal==1.3.2
anyio==4.9.0
asgiref==3.8.1

# Redis and caching
redis==7.0.0
aioredis==2.0.1
cachetools==5.5.2

# Data processing and validation
pydantic-core==2.33.2
orjson==3.10.18
python-multipart==0.0.12
attrs==25.3.0

# Security and authentication
cryptography==44.0.2
python-jose[cryptography]==3.3.0
bcrypt==4.3.0

# Monitoring and logging
loguru==0.7.3
psutil==7.0.0
rich==14.0.0
coloredlogs==15.0.1

# HTTP and networking
certifi==2025.4.26
urllib3==2.4.0
charset-normalizer==3.4.1
idna==3.10
h11==0.16.0
h2==4.2.0
hpack==4.1.0
httpcore==1.0.9
httptools==0.6.4
httpx-sse==0.4.0

# Development and testing
pytest==8.3.5
pytest-asyncio==1.0.0
pytest-mock==3.14.0

# Utilities and extras
python-dateutil==2.9.0.post0
pytz==2025.2
tenacity==9.1.2
backoff==2.2.1
click==8.1.8
colorama==0.4.6
filelock==3.18.0
frozenlist==1.6.0
jinja2==3.1.6
markupsafe==3.0.2
multidict==6.4.3
packaging==24.2
six==1.17.0
sniffio==1.3.1
typing-extensions==4.13.2
yarl==1.20.0
watchfiles==1.0.5
websockets==14.2

# Additional crawling and stealth capabilities
fake-useragent==2.2.0
fake-http-header==0.3.5
tf-playwright-stealth==1.1.2
cssselect==1.3.0
soupsieve==2.7

# Data processing and ML support
numpy==2.2.5
pandas==2.2.3
regex==2024.11.6
chardet==5.2.0

# Additional async and networking
brotli==1.1.0
starlette==0.45.3
sse-starlette==2.3.3

# Configuration and utilities
toml==0.10.2
iniconfig==2.1.0
shellingham==1.5.4
distro==1.9.0
