#!/usr/bin/env python3
"""
Verification script for package updates in multi-agent-crawler
Checks if all updated packages can be imported and their versions
"""

import sys
import subprocess
import importlib.metadata
from typing import Dict, List, Tuple

# Expected versions after update
EXPECTED_VERSIONS = {
    # Core Framework
    "fastapi": "0.115.12",
    "uvicorn": "0.32.1", 
    "pydantic": "2.10.3",
    "python-dotenv": "1.0.1",
    
    # AI & Crawling
    "crewai": "0.126.0",
    "crawl4ai": "0.6.3",
    "langchain": "0.3.12",
    "langchain-openai": "0.2.14",%
    "playwright": "1.52.0",
    "beautifulsoup4": "4.12.3",
    "requests": "2.32.3",
    "httpx": "0.27.2",
    
    # Search APIs
    "exa-py": "1.1.0",
    "brave-search": "0.1.8",
    
    # Database & Storage
    "redis": "5.2.1",
    "sqlalchemy": "2.0.36",
    "alembic": "1.14.0",
    "asyncpg": "0.30.0",
    
    # Data Processing
    "pandas": "2.2.3",
    "numpy": "1.26.4",
    "python-multipart": "0.0.12",
    
    # UI & Visualization
    "gradio": "5.9.1",
    "streamlit": "1.41.0",
    "plotly": "5.24.1",
    
    # Utilities
    "celery": "5.4.0",
    "schedule": "1.2.2",
    "python-crontab": "3.2.0",
    "loguru": "0.7.2",
    
    # Testing
    "pytest": "8.3.4",
    "pytest-asyncio": "1.0.0"
}

def get_installed_version(package_name: str) -> str:
    """Get the installed version of a package"""
    try:
        return importlib.metadata.version(package_name)
    except importlib.metadata.PackageNotFoundError:
        return "NOT_INSTALLED"

def check_package_versions() -> Tuple[List[str], List[str], List[str]]:
    """Check all package versions against expected versions"""
    correct = []
    incorrect = []
    missing = []
    
    print("🔍 Checking package versions...")
    print("=" * 60)
    
    for package, expected_version in EXPECTED_VERSIONS.items():
        installed_version = get_installed_version(package)
        
        if installed_version == "NOT_INSTALLED":
            missing.append(f"{package}: NOT INSTALLED")
            print(f"❌ {package:<20} NOT INSTALLED (expected {expected_version})")
        elif installed_version == expected_version:
            correct.append(f"{package}: {installed_version}")
            print(f"✅ {package:<20} {installed_version}")
        else:
            incorrect.append(f"{package}: {installed_version} (expected {expected_version})")
            print(f"⚠️  {package:<20} {installed_version} (expected {expected_version})")
    
    return correct, incorrect, missing

def test_critical_imports() -> List[str]:
    """Test importing critical packages"""
    critical_packages = [
        "fastapi",
        "crewai", 
        "crawl4ai",
        "gradio",
        "streamlit",
        "pandas",
        "numpy",
        "redis",
        "playwright"
    ]
    
    failed_imports = []
    print("\n🧪 Testing critical package imports...")
    print("=" * 60)
    
    for package in critical_packages:
        try:
            __import__(package)
            print(f"✅ {package:<20} Import successful")
        except ImportError as e:
            failed_imports.append(f"{package}: {str(e)}")
            print(f"❌ {package:<20} Import failed: {str(e)}")
    
    return failed_imports

def generate_report(correct: List[str], incorrect: List[str], missing: List[str], failed_imports: List[str]):
    """Generate a comprehensive report"""
    print("\n📊 VERIFICATION REPORT")
    print("=" * 60)
    
    total_packages = len(EXPECTED_VERSIONS)
    correct_count = len(correct)
    incorrect_count = len(incorrect)
    missing_count = len(missing)
    
    print(f"Total packages checked: {total_packages}")
    print(f"✅ Correct versions: {correct_count}")
    print(f"⚠️  Incorrect versions: {incorrect_count}")
    print(f"❌ Missing packages: {missing_count}")
    print(f"🧪 Failed imports: {len(failed_imports)}")
    
    success_rate = (correct_count / total_packages) * 100
    print(f"\n📈 Success rate: {success_rate:.1f}%")
    
    if incorrect:
        print(f"\n⚠️  INCORRECT VERSIONS:")
        for item in incorrect:
            print(f"   - {item}")
    
    if missing:
        print(f"\n❌ MISSING PACKAGES:")
        for item in missing:
            print(f"   - {item}")
    
    if failed_imports:
        print(f"\n🚨 FAILED IMPORTS:")
        for item in failed_imports:
            print(f"   - {item}")
    
    if success_rate == 100 and not failed_imports:
        print(f"\n🎉 ALL PACKAGES SUCCESSFULLY UPDATED AND VERIFIED!")
        return True
    else:
        print(f"\n🔧 Some packages need attention. Check the details above.")
        return False

def main():
    """Main verification function"""
    print("🚀 Multi-Agent Crawler Package Update Verification")
    print("=" * 60)
    print("Checking all updated packages for version compliance and import capability...")
    
    # Check versions
    correct, incorrect, missing = check_package_versions()
    
    # Test imports
    failed_imports = test_critical_imports()
    
    # Generate report
    success = generate_report(correct, incorrect, missing, failed_imports)
    
    # Exit with appropriate code
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
